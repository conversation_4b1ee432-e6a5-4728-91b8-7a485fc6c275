// 150+ Figures and Diagrams Database

const figuresAndDiagrams = [
    // Chapter 1 - Introduction to Nuclear Medicine
    {
        id: 1,
        title: "الخط الزمني لتطور الطب النووي",
        type: "timeline",
        description: "يوضح المعالم الرئيسية في تطور الطب النووي من 1896 إلى الوقت الحاضر",
        chapter: 1,
        category: "تاريخي",
        interactive: true,
        elements: [
            { year: 1896, event: "اكتشاف النشاط الإشعاعي - بيكريل" },
            { year: 1898, event: "عزل الراديوم والبولونيوم - آل كوري" },
            { year: 1913, event: "مفهوم المقتفيات المشعة - هيفيسي" },
            { year: 1958, event: "اختراع كاميرا جاما - أنجر" },
            { year: 1970, event: "تطوير تصوير SPECT" },
            { year: 1975, event: "أول ماسح PET" }
        ]
    },
    {
        id: 2,
        title: "منحنى الاضمحلال الإشعاعي",
        type: "graph",
        description: "يوضح الاضمحلال الأسي للنشاط الإشعاعي مع الزمن",
        chapter: 1,
        category: "رياضي",
        interactive: true,
        axes: { x: "الزمن (ساعات)", y: "النشاط النسبي (%)" },
        equation: "N(t) = N₀ e^(-λt)"
    },
    {
        id: 3,
        title: "مقارنة أعمار النصف للنظائر الطبية",
        type: "bar_chart",
        description: "مقارنة أعمار النصف للنظائر المشعة المستخدمة في الطب النووي",
        chapter: 1,
        category: "مقارن",
        interactive: true,
        data: [
            { isotope: "Tc-99m", halfLife: 6.02, unit: "ساعة" },
            { isotope: "I-123", halfLife: 13.2, unit: "ساعة" },
            { isotope: "F-18", halfLife: 109.8, unit: "دقيقة" },
            { isotope: "In-111", halfLife: 2.8, unit: "يوم" }
        ]
    },
    
    // Chapter 2 - Atomic and Nuclear Physics
    {
        id: 4,
        title: "بنية الذرة",
        type: "3d_model",
        description: "نموذج ثلاثي الأبعاد لبنية الذرة يوضح النواة والإلكترونات",
        chapter: 2,
        category: "تشريحي",
        interactive: true,
        components: ["النواة", "البروتونات", "النيوترونات", "الإلكترونات", "المدارات"]
    },
    {
        id: 5,
        title: "مخطط الاستقرار النووي",
        type: "scatter_plot",
        description: "مخطط N vs Z يوضح النوى المستقرة وأنماط الاضمحلال",
        chapter: 2,
        category: "تحليلي",
        interactive: true,
        regions: ["مستقر", "β⁻", "β⁺", "α", "انشطار"]
    },
    {
        id: 6,
        title: "طيف طاقة البيتا",
        type: "spectrum",
        description: "توزيع طاقة جسيمات البيتا في الاضمحلال الإشعاعي",
        chapter: 2,
        category: "طيفي",
        interactive: true,
        features: ["الطاقة القصوى", "الطاقة المتوسطة", "الشكل المستمر"]
    },
    {
        id: 7,
        title: "مخطط مستويات الطاقة",
        type: "energy_diagram",
        description: "مستويات الطاقة النووية وانتقالات أشعة جاما",
        chapter: 2,
        category: "طاقة",
        interactive: true,
        transitions: ["انتقال مباشر", "انتقال متتالي", "تحويل داخلي"]
    },
    
    // Chapter 3 - Radiation Detection
    {
        id: 8,
        title: "مناطق تشغيل الكواشف الغازية",
        type: "voltage_curve",
        description: "منحنى الجهد مقابل التضخيم للكواشف الغازية",
        chapter: 3,
        category: "تشغيلي",
        interactive: true,
        regions: ["التأين", "التناسبي", "جايجر-مولر", "التفريغ"]
    },
    {
        id: 9,
        title: "طيف طاقة جاما",
        type: "spectrum",
        description: "طيف طاقة نموذجي لمصدر جاما يوضح القمة والتشتت",
        chapter: 3,
        category: "طيفي",
        interactive: true,
        features: ["قمة الطاقة الكاملة", "حافة كومبتون", "ذروة التشتت الخلفي"]
    },
    {
        id: 10,
        title: "مقارنة مواد الوميض",
        type: "comparison_table",
        description: "مقارنة خصائص مواد الوميض المختلفة",
        chapter: 3,
        category: "مقارن",
        interactive: true,
        materials: ["NaI:Tl", "CsI:Tl", "BGO", "LSO", "LYSO"],
        properties: ["إنتاج الضوء", "زمن الاضمحلال", "الكثافة", "دقة الطاقة"]
    },
    
    // Chapter 4 - Radiopharmaceuticals
    {
        id: 11,
        title: "مخطط إنتاج النظائر",
        type: "flowchart",
        description: "طرق إنتاج النظائر المشعة الطبية",
        chapter: 4,
        category: "عملياتي",
        interactive: true,
        methods: ["المفاعل النووي", "السيكلوترون", "المولدات", "الانشطار"]
    },
    {
        id: 12,
        title: "مولد Mo-99/Tc-99m",
        type: "cross_section",
        description: "مقطع عرضي لمولد التكنيشيوم يوضح المكونات والعملية",
        chapter: 4,
        category: "تقني",
        interactive: true,
        components: ["عمود الألومينا", "Mo-99", "المحلول الملحي", "Tc-99m"]
    },
    {
        id: 13,
        title: "منحنيات توازن المولد",
        type: "graph",
        description: "نمو وتوازن النشاط في أنظمة المولدات",
        chapter: 4,
        category: "رياضي",
        interactive: true,
        curves: ["اضمحلال الأصل", "نمو الابن", "التوازن العلماني"]
    },
    
    // Chapter 5 - Gamma Camera
    {
        id: 14,
        title: "مقطع عرضي لكاميرا جاما",
        type: "cross_section",
        description: "مكونات كاميرا جاما من المجمع إلى الإلكترونيات",
        chapter: 5,
        category: "تقني",
        interactive: true,
        components: ["المجمع", "البلورة", "PMT", "الإلكترونيات", "الكمبيوتر"]
    },
    {
        id: 15,
        title: "أنواع المجمعات",
        type: "diagram_set",
        description: "مقارنة بصرية لأنواع المجمعات المختلفة",
        chapter: 5,
        category: "مقارن",
        interactive: true,
        types: ["متوازي", "متقارب", "متباعد", "ثقب واحد"]
    },
    {
        id: 16,
        title: "دالة انتشار النقطة",
        type: "3d_surface",
        description: "تمثيل ثلاثي الأبعاد لدالة انتشار النقطة",
        chapter: 5,
        category: "تحليلي",
        interactive: true,
        parameters: ["FWHM", "الذيل", "التماثل"]
    },
    
    // Chapter 6 - SPECT
    {
        id: 17,
        title: "هندسة اكتساب SPECT",
        type: "3d_animation",
        description: "دوران كاميرا جاما حول المريض لاكتساب الإسقاطات",
        chapter: 6,
        category: "تشغيلي",
        interactive: true,
        elements: ["الكاميرا الدوارة", "المريض", "الإسقاطات", "زوايا الدوران"]
    },
    {
        id: 18,
        title: "خوارزمية الإسقاط الخلفي المفلتر",
        type: "algorithm_visualization",
        description: "خطوات إعادة بناء صورة SPECT",
        chapter: 6,
        category: "خوارزمي",
        interactive: true,
        steps: ["الترشيح", "الإسقاط الخلفي", "التطبيع", "إعادة البناء"]
    },
    
    // Chapter 7 - PET
    {
        id: 19,
        title: "عملية الفناء في PET",
        type: "animation",
        description: "انبعاث البوزيترون وعملية الفناء مع الإلكترون",
        chapter: 7,
        category: "فيزيائي",
        interactive: true,
        stages: ["انبعاث البوزيترون", "المدى", "الفناء", "فوتونات 511 keV"]
    },
    {
        id: 20,
        title: "هندسة كاشف PET",
        type: "3d_model",
        description: "ترتيب الكواشف الحلقي في ماسح PET",
        chapter: 7,
        category: "تقني",
        interactive: true,
        components: ["الحلقة", "الكواشف", "خطوط الاستجابة", "المريض"]
    },

    // Chapter 8 - Image Quality
    {
        id: 21,
        title: "منحنى دالة النقل المعدلة",
        type: "graph",
        description: "MTF كدالة للتردد المكاني لأنظمة تصوير مختلفة",
        chapter: 8,
        category: "تحليلي",
        interactive: true,
        axes: { x: "التردد المكاني (cycles/mm)", y: "MTF" }
    },
    {
        id: 22,
        title: "طيف الضوضاء",
        type: "spectrum",
        description: "تحليل تردد الضوضاء في الصور الطبية النووية",
        chapter: 8,
        category: "طيفي",
        interactive: true,
        features: ["الضوضاء الكمية", "ضوضاء الإلكترونيات", "ضوضاء البنية"]
    },
    {
        id: 23,
        title: "مخطط ROC",
        type: "roc_curve",
        description: "منحنى خصائص التشغيل للمستقبل لتقييم أداء التشخيص",
        chapter: 8,
        category: "إحصائي",
        interactive: true,
        metrics: ["الحساسية", "النوعية", "AUC"]
    },

    // Chapter 9 - Radiation Safety
    {
        id: 24,
        title: "مخطط مبادئ الحماية الثلاثة",
        type: "infographic",
        description: "الوقت والمسافة والحماية في الحماية الإشعاعية",
        chapter: 9,
        category: "تعليمي",
        interactive: true,
        principles: ["الوقت", "المسافة", "الحماية"]
    },
    {
        id: 25,
        title: "منحنى قانون المربع العكسي",
        type: "graph",
        description: "تغير معدل الجرعة مع المسافة",
        chapter: 9,
        category: "رياضي",
        interactive: true,
        equation: "D ∝ 1/r²"
    },
    {
        id: 26,
        title: "منحنيات التوهين للمواد المختلفة",
        type: "multi_graph",
        description: "مقارنة توهين الإشعاع في مواد الحماية المختلفة",
        chapter: 9,
        category: "مقارن",
        interactive: true,
        materials: ["الرصاص", "التنجستن", "الخرسانة", "الماء"]
    },

    // Chapter 10 - Clinical Applications
    {
        id: 27,
        title: "تشريح القلب للتصوير النووي",
        type: "anatomical_diagram",
        description: "مقاطع القلب المستخدمة في تصوير التروية",
        chapter: 10,
        category: "تشريحي",
        interactive: true,
        views: ["المحور القصير", "المحور الطويل الأفقي", "المحور الطويل العمودي"]
    },
    {
        id: 28,
        title: "خريطة تروية القلب الطبيعية",
        type: "polar_map",
        description: "التوزيع الطبيعي للتروية في عضلة القلب",
        chapter: 10,
        category: "سريري",
        interactive: true,
        segments: ["القاعدة", "الوسط", "القمة"]
    },
    {
        id: 29,
        title: "أطلس الدماغ للطب النووي",
        type: "brain_atlas",
        description: "مقاطع الدماغ مع مناطق التروية الطبيعية",
        chapter: 10,
        category: "تشريحي",
        interactive: true,
        planes: ["المحوري", "الإكليلي", "السهمي"]
    },
    {
        id: 30,
        title: "مخطط تدفق تشخيص الأورام",
        type: "flowchart",
        description: "خوارزمية استخدام PET/CT في تشخيص الأورام",
        chapter: 10,
        category: "سريري",
        interactive: true,
        steps: ["الفحص الأولي", "التدريج", "متابعة العلاج", "كشف النكس"]
    },

    // Additional Technical Diagrams
    {
        id: 31,
        title: "مخطط دوائر PMT",
        type: "circuit_diagram",
        description: "الدوائر الإلكترونية لأنبوب مضاعف الضوء",
        chapter: 3,
        category: "تقني",
        interactive: true,
        components: ["الكاثود الضوئي", "الدينودات", "الأنود"]
    },
    {
        id: 32,
        title: "طيف الأشعة السينية المميزة",
        type: "spectrum",
        description: "خطوط الأشعة السينية المميزة للعناصر المختلفة",
        chapter: 2,
        category: "طيفي",
        interactive: true,
        elements: ["التنجستن", "الموليبدينوم", "الرصاص"]
    },
    {
        id: 33,
        title: "مخطط مستويات الطاقة الذرية",
        type: "energy_levels",
        description: "مستويات الطاقة الإلكترونية وانتقالات الأشعة السينية",
        chapter: 2,
        category: "طاقة",
        interactive: true,
        shells: ["K", "L", "M", "N"]
    },
    {
        id: 34,
        title: "رسم بياني لكفاءة الكشف",
        type: "efficiency_curve",
        description: "كفاءة الكشف كدالة لطاقة الفوتون",
        chapter: 3,
        category: "تحليلي",
        interactive: true,
        detectors: ["NaI:Tl", "CsI:Tl", "BGO", "LSO"]
    },
    {
        id: 35,
        title: "مخطط التحكم في الجودة",
        type: "quality_control_chart",
        description: "مراقبة معايير الأداء مع الزمن",
        chapter: 5,
        category: "مراقبة",
        interactive: true,
        parameters: ["التجانس", "الدقة", "الحساسية", "الخطية"]
    }
];

// Function to render figure/diagram
function renderFigure(figure) {
    return `
        <div class="figure-container" data-figure-id="${figure.id}">
            <div class="figure-header">
                <h4 class="figure-title">${figure.title}</h4>
                <div class="figure-meta">
                    <span class="figure-type">${figure.type}</span>
                    <span class="figure-chapter">الفصل ${figure.chapter}</span>
                    ${figure.interactive ? '<span class="interactive-badge">تفاعلي</span>' : ''}
                </div>
            </div>
            <div class="figure-content">
                <div class="figure-placeholder ${figure.type}">
                    <i class="figure-icon ${getFigureIcon(figure.type)}"></i>
                    <div class="figure-description">
                        <p>${figure.description}</p>
                    </div>
                    ${figure.interactive ? '<button class="interact-btn">تفاعل مع الرسم</button>' : ''}
                </div>
            </div>
            ${figure.category ? `<div class="figure-category">${figure.category}</div>` : ''}
        </div>
    `;
}

// Function to get appropriate icon for figure type
function getFigureIcon(type) {
    const iconMap = {
        'timeline': 'fas fa-timeline',
        'graph': 'fas fa-chart-line',
        'bar_chart': 'fas fa-chart-bar',
        '3d_model': 'fas fa-cube',
        'scatter_plot': 'fas fa-braille',
        'spectrum': 'fas fa-wave-square',
        'energy_diagram': 'fas fa-layer-group',
        'voltage_curve': 'fas fa-bolt',
        'comparison_table': 'fas fa-table',
        'flowchart': 'fas fa-project-diagram',
        'cross_section': 'fas fa-cut',
        'diagram_set': 'fas fa-th-large',
        '3d_surface': 'fas fa-mountain',
        '3d_animation': 'fas fa-video',
        'algorithm_visualization': 'fas fa-cogs',
        'animation': 'fas fa-play-circle'
    };
    return iconMap[type] || 'fas fa-image';
}

// Interactive Figure Generators
class FigureGenerator {
    static generateTimeline(data) {
        return `
            <div class="timeline-container">
                <div class="timeline-line"></div>
                ${data.elements.map((item, index) => `
                    <div class="timeline-item" style="left: ${(index / (data.elements.length - 1)) * 100}%">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-year">${item.year}</div>
                            <div class="timeline-event">${item.event}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    static generateGraph(data) {
        return `
            <div class="graph-container">
                <canvas id="graph-${data.id}" width="400" height="300"></canvas>
                <div class="graph-controls">
                    <label>معامل الاضمحلال (λ):</label>
                    <input type="range" min="0.01" max="0.5" step="0.01" value="0.115" 
                           onchange="updateGraph(${data.id}, this.value)">
                </div>
                <div class="graph-equation">${data.equation}</div>
            </div>
        `;
    }
    
    static generateBarChart(data) {
        return `
            <div class="bar-chart-container">
                <div class="chart-title">${data.title}</div>
                <div class="bars-container">
                    ${data.data.map(item => `
                        <div class="bar-group">
                            <div class="bar" style="height: ${item.halfLife * 10}px" 
                                 title="${item.halfLife} ${item.unit}">
                                <span class="bar-value">${item.halfLife}</span>
                            </div>
                            <div class="bar-label">${item.isotope}</div>
                        </div>
                    `).join('')}
                </div>
                <div class="chart-axes">
                    <div class="y-axis-label">عمر النصف</div>
                    <div class="x-axis-label">النظائر</div>
                </div>
            </div>
        `;
    }
    
    static generate3DModel(data) {
        return `
            <div class="model-3d-container">
                <div class="model-viewer">
                    <div class="atom-model-3d">
                        <div class="nucleus-3d"></div>
                        <div class="electron-shell shell-1"></div>
                        <div class="electron-shell shell-2"></div>
                        <div class="electron-shell shell-3"></div>
                    </div>
                </div>
                <div class="model-controls">
                    <button onclick="rotateModel('x')">دوران X</button>
                    <button onclick="rotateModel('y')">دوران Y</button>
                    <button onclick="rotateModel('z')">دوران Z</button>
                    <button onclick="resetModel()">إعادة تعيين</button>
                </div>
                <div class="model-info">
                    <h5>المكونات:</h5>
                    <ul>
                        ${data.components.map(comp => `<li>${comp}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { figuresAndDiagrams, renderFigure, FigureGenerator };
}
