# منصة التصوير الطبي النووي التفاعلية
## Nuclear Medical Imaging Physics and Technology - Interactive Platform

### 📖 نظرة عامة
منصة تعليمية تفاعلية شاملة لتعلم فيزياء وتكنولوجيا التصوير الطبي النووي. تجمع المنصة بين المحتوى النظري العلمي الدقيق والعناصر التفاعلية المبتكرة لتوفير تجربة تعليمية متميزة.

### 🎯 الأهداف التعليمية
- فهم المبادئ الأساسية للفيزياء النووية والإشعاع
- تعلم تقنيات كشف وقياس الإشعاع
- إتقان مبادئ التصوير الطبي النووي
- فهم السلامة الإشعاعية والحماية
- تطبيق المعرفة في الممارسة السريرية

### 🏗️ بنية المشروع
```
Nuclear Medical Imaging Physics and Technology/
├── index.html              # الصفحة الرئيسية
├── styles.css              # ملف الأنماط الرئيسي
├── script.js               # الوظائف الأساسية
├── chapters.js             # محتوى الفصول التفصيلي
├── interactive.js          # العناصر التفاعلية والمحاكيات
└── README.md              # توثيق المشروع
```

### 📚 الفصول المتاحة

#### الفصل الأول: مقدمة في الطب النووي
- التطور التاريخي للطب النووي
- المبادئ الأساسية للفيزياء النووية
- دور الطب النووي في الرعاية الصحية
- **العناصر التفاعلية**: خط زمني تفاعلي، محاكي الاضمحلال

#### الفصل الثاني: الفيزياء الذرية والنووية
- بنية الذرة والنواة
- أنواع الاضمحلال الإشعاعي
- حساب ثوابت الاضمحلال والأنشطة
- **العناصر التفاعلية**: نموذج ذري ثلاثي الأبعاد، مخططات الاضمحلال

#### الفصل الثالث: كشف وقياس الإشعاع
- مبادئ كشف الإشعاع
- أجهزة الكشف الغازية والصلبة
- خصائص الأداء ومراقبة الجودة
- **العناصر التفاعلية**: محاكي استجابة الكاشف، حاسبة الوقت الميت

#### الفصل الرابع: المستحضرات الإشعاعية
- مبادئ تصميم المستحضرات الإشعاعية
- طرق إنتاج النظائر المشعة
- أنظمة المولدات الإشعاعية
- **العناصر التفاعلية**: نماذج جزيئية ثلاثية الأبعاد، محاكي المولد

#### الفصل الخامس: أنظمة التصوير - كاميرا جاما
- مكونات كاميرا جاما وتصميمها
- تكوين الصورة والدقة المكانية
- خصائص الأداء ومراقبة الجودة
- **العناصر التفاعلية**: نموذج كاميرا ثلاثي الأبعاد، محلل الدقة

### 🎮 المميزات التفاعلية

#### المحاكيات العلمية
- **محاكي الاضمحلال الإشعاعي**: يوضح عملية الاضمحلال بصرياً
- **نموذج الذرة التفاعلي**: استكشاف بنية الذرة والمدارات الإلكترونية
- **محاكي كاشف الإشعاع**: تعلم كيفية عمل كواشف الإشعاع
- **محاكي توازن المولد**: فهم ديناميكيات الأصل-الابن

#### الحاسبات العلمية
- حاسبة النشاط الإشعاعي وعمر النصف
- حاسبة تصحيح الوقت الميت
- حاسبة دقة الكاشف والكفاءة
- حاسبة الجرعات الإشعاعية

#### العناصر البصرية
- رسوم بيانية تفاعلية
- مخططات ثلاثية الأبعاد
- أنيميشن للعمليات الفيزيائية
- جداول مقارنة تفاعلية

### 🛠️ التقنيات المستخدمة

#### التقنيات الأساسية
- **HTML5**: بنية المحتوى الدلالية
- **CSS3**: التصميم المتجاوب والأنيميشن
- **JavaScript ES6+**: التفاعلية والمحاكيات

#### المكتبات الخارجية
- **KaTeX**: عرض المعادلات الرياضية
- **Font Awesome**: الأيقونات
- **Google Fonts**: الخطوط العربية (Cairo)

#### المميزات التقنية
- تصميم متجاوب (Responsive Design)
- دعم الأجهزة المحمولة
- تحسين الأداء والسرعة
- إمكانية الوصول (Accessibility)

### 🎨 التصميم والواجهة

#### نظام الألوان
- **اللون الأساسي**: أزرق (#2563eb)
- **اللون الثانوي**: أزرق داكن (#1e40af)
- **لون النجاح**: أخضر (#10b981)
- **لون التحذير**: برتقالي (#f59e0b)

#### الخطوط
- **الخط الأساسي**: Cairo (عربي)
- **الخط الثانوي**: Inter (إنجليزي)
- **خط المعادلات**: Times New Roman

#### التخطيط
- تخطيط شبكي مرن (CSS Grid)
- نظام فليكس بوكس للمحاذاة
- مساحات بيضاء متوازنة
- تدرجات لونية جذابة

### 📱 الاستجابة للأجهزة

#### نقاط التوقف (Breakpoints)
- **الهواتف**: < 768px
- **الأجهزة اللوحية**: 768px - 1024px
- **أجهزة الكمبيوتر**: > 1024px

#### التحسينات المحمولة
- قائمة تنقل قابلة للطي
- أزرار محسنة للمس
- محتوى قابل للتمرير
- تحميل محسن للصور

### 🔧 التثبيت والتشغيل

#### متطلبات النظام
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- اتصال بالإنترنت (للخطوط والمكتبات الخارجية)

#### خطوات التشغيل
1. تحميل ملفات المشروع
2. فتح ملف `index.html` في المتصفح
3. أو استخدام خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   ```

### 🎓 الاستخدام التعليمي

#### للطلاب
- تصفح الفصول بالترتيب
- استخدام العناصر التفاعلية للفهم العميق
- حل التمارين والمحاكيات
- مراجعة المعادلات والمفاهيم

#### للمدرسين
- استخدام المحاكيات في الشرح
- إنشاء واجبات تفاعلية
- تتبع تقدم الطلاب
- تخصيص المحتوى حسب المستوى

### 🔮 التطوير المستقبلي

#### الفصول القادمة
- الفصل السادس: تصوير SPECT
- الفصل السابع: تصوير PET
- الفصل الثامن: جودة الصورة والتحف
- الفصل التاسع: السلامة الإشعاعية
- الفصل العاشر: التطبيقات السريرية

#### المميزات المخططة
- نظام اختبارات تفاعلي
- تتبع التقدم الشخصي
- منتدى للنقاش والأسئلة
- تطبيق محمول مصاحب
- محتوى فيديو تعليمي

### 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

### 🤝 المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

### 📞 التواصل
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.nuclear-imaging-physics.edu

---
© 2024 منصة التصوير الطبي النووي. جميع الحقوق محفوظة.
