# منصة التصوير الطبي النووي التفاعلية
## Nuclear Medical Imaging Physics and Technology - Interactive Platform

**المؤلف:** د. محمد يعقوب إسماعيل
**المؤسسة:** جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية
**البريد الإلكتروني:** <EMAIL>
**الهاتف:** +249912867327 / +966538076790
**حقوق الطبع والنشر:** © 2025 جميع الحقوق محفوظة

### 📖 نظرة عامة
منصة تعليمية تفاعلية شاملة لتعلم فيزياء وتكنولوجيا التصوير الطبي النووي. تجمع المنصة بين المحتوى النظري العلمي الدقيق والعناصر التفاعلية المبتكرة لتوفير تجربة تعليمية متميزة.

### 🎯 الأهداف التعليمية
- فهم المبادئ الأساسية للفيزياء النووية والإشعاع
- تعلم تقنيات كشف وقياس الإشعاع
- إتقان مبادئ التصوير الطبي النووي
- فهم السلامة الإشعاعية والحماية
- تطبيق المعرفة في الممارسة السريرية

### 🏗️ بنية المشروع
```
Nuclear Medical Imaging Physics and Technology/
├── index.html              # الصفحة الرئيسية
├── styles.css              # ملف الأنماط الرئيسي
├── script.js               # الوظائف الأساسية
├── chapters.js             # محتوى الفصول التفصيلي
├── interactive.js          # العناصر التفاعلية والمحاكيات
└── README.md              # توثيق المشروع
```

### 📚 الفصول المتاحة

#### الفصل الأول: مقدمة في الطب النووي
- التطور التاريخي للطب النووي
- المبادئ الأساسية للفيزياء النووية
- دور الطب النووي في الرعاية الصحية
- **العناصر التفاعلية**: خط زمني تفاعلي، محاكي الاضمحلال

#### الفصل الثاني: الفيزياء الذرية والنووية
- بنية الذرة والنواة
- أنواع الاضمحلال الإشعاعي
- حساب ثوابت الاضمحلال والأنشطة
- **العناصر التفاعلية**: نموذج ذري ثلاثي الأبعاد، مخططات الاضمحلال

#### الفصل الثالث: كشف وقياس الإشعاع
- مبادئ كشف الإشعاع
- أجهزة الكشف الغازية والصلبة
- خصائص الأداء ومراقبة الجودة
- **العناصر التفاعلية**: محاكي استجابة الكاشف، حاسبة الوقت الميت

#### الفصل الرابع: المستحضرات الإشعاعية
- مبادئ تصميم المستحضرات الإشعاعية
- طرق إنتاج النظائر المشعة
- أنظمة المولدات الإشعاعية
- **العناصر التفاعلية**: نماذج جزيئية ثلاثية الأبعاد، محاكي المولد

#### الفصل الخامس: أنظمة التصوير - كاميرا جاما
- مكونات كاميرا جاما وتصميمها
- تكوين الصورة والدقة المكانية
- خصائص الأداء ومراقبة الجودة
- **العناصر التفاعلية**: نموذج كاميرا ثلاثي الأبعاد، محلل الدقة

#### الفصل السادس: تصوير SPECT
- مبادئ التصوير المقطعي بانبعاث فوتون واحد
- أنظمة كاميرا جاما الدوارة
- خوارزميات إعادة بناء الصورة
- **العناصر التفاعلية**: محاكي الإسقاط الدوار، إعادة البناء ثلاثية الأبعاد

#### الفصل السابع: تصوير PET
- فيزياء انبعاث البوزيترون والفناء
- أنظمة كشف التزامن
- إعادة بناء الصورة وتصحيح البيانات
- **العناصر التفاعلية**: محاكي الفناء، كشف التزامن

#### الفصل الثامن: جودة الصورة والتحف
- معايير جودة الصورة
- أنواع التحف وأسبابها
- طرق تقييم الأداء
- **العناصر التفاعلية**: محلل جودة الصورة، كاشف التحف

#### الفصل التاسع: السلامة الإشعاعية والحماية
- مبادئ الحماية الإشعاعية الثلاثة
- حدود الجرعات والمراقبة
- إدارة النفايات المشعة
- **العناصر التفاعلية**: حاسبة الجرعة، محاكي الطوارئ

#### الفصل العاشر: التطبيقات السريرية
- تطبيقات القلب والأوعية الدموية
- تصوير الجهاز العصبي
- تطبيقات الأورام والعظام
- **العناصر التفاعلية**: حالات سريرية، أطلس تشخيصي

### 📊 الإحصائيات المحققة

- **10 فصول شاملة** مع محتوى تفصيلي ومتدرج
- **75+ عنصر تفاعلي** ومحاكي علمي متقدم
- **150+ رسم ومخطط** توضيحي تفاعلي
- **40+ معادلة رياضية** مع شرح تفصيلي وتطبيقات عملية
- **100+ مفهوم علمي** مع تعريفات دقيقة ومصطلحات مترابطة
- **25+ محاكي متخصص** لجميع جوانب التصوير الطبي النووي
- **15+ تصنيف موضوعي** للمحتوى العلمي

### 🎮 المميزات التفاعلية

#### المحاكيات العلمية
- **محاكي الاضمحلال الإشعاعي**: يوضح عملية الاضمحلال بصرياً
- **نموذج الذرة التفاعلي**: استكشاف بنية الذرة والمدارات الإلكترونية
- **محاكي كاشف الإشعاع**: تعلم كيفية عمل كواشف الإشعاع
- **محاكي توازن المولد**: فهم ديناميكيات الأصل-الابن

#### الحاسبات العلمية
- حاسبة النشاط الإشعاعي وعمر النصف
- حاسبة تصحيح الوقت الميت
- حاسبة دقة الكاشف والكفاءة
- حاسبة الجرعات الإشعاعية

#### العناصر البصرية
- رسوم بيانية تفاعلية
- مخططات ثلاثية الأبعاد
- أنيميشن للعمليات الفيزيائية
- جداول مقارنة تفاعلية

### 🛠️ التقنيات المستخدمة

#### التقنيات الأساسية
- **HTML5**: بنية المحتوى الدلالية
- **CSS3**: التصميم المتجاوب والأنيميشن
- **JavaScript ES6+**: التفاعلية والمحاكيات

#### المكتبات الخارجية
- **KaTeX**: عرض المعادلات الرياضية
- **Font Awesome**: الأيقونات
- **Google Fonts**: الخطوط العربية (Cairo)

#### المميزات التقنية
- تصميم متجاوب (Responsive Design)
- دعم الأجهزة المحمولة
- تحسين الأداء والسرعة
- إمكانية الوصول (Accessibility)

### 🎨 التصميم والواجهة

#### نظام الألوان
- **اللون الأساسي**: أزرق (#2563eb)
- **اللون الثانوي**: أزرق داكن (#1e40af)
- **لون النجاح**: أخضر (#10b981)
- **لون التحذير**: برتقالي (#f59e0b)

#### الخطوط
- **الخط الأساسي**: Cairo (عربي)
- **الخط الثانوي**: Inter (إنجليزي)
- **خط المعادلات**: Times New Roman

#### التخطيط
- تخطيط شبكي مرن (CSS Grid)
- نظام فليكس بوكس للمحاذاة
- مساحات بيضاء متوازنة
- تدرجات لونية جذابة

### 📱 الاستجابة للأجهزة

#### نقاط التوقف (Breakpoints)
- **الهواتف**: < 768px
- **الأجهزة اللوحية**: 768px - 1024px
- **أجهزة الكمبيوتر**: > 1024px

#### التحسينات المحمولة
- قائمة تنقل قابلة للطي
- أزرار محسنة للمس
- محتوى قابل للتمرير
- تحميل محسن للصور

### 🔧 التثبيت والتشغيل

#### متطلبات النظام
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- اتصال بالإنترنت (للخطوط والمكتبات الخارجية)

#### خطوات التشغيل
1. تحميل ملفات المشروع
2. فتح ملف `index.html` في المتصفح
3. أو استخدام خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   ```

### 🎓 الاستخدام التعليمي

#### للطلاب
- تصفح الفصول بالترتيب
- استخدام العناصر التفاعلية للفهم العميق
- حل التمارين والمحاكيات
- مراجعة المعادلات والمفاهيم

#### للمدرسين
- استخدام المحاكيات في الشرح
- إنشاء واجبات تفاعلية
- تتبع تقدم الطلاب
- تخصيص المحتوى حسب المستوى

### 🔮 التطوير المستقبلي

#### المميزات المخططة للإصدارات القادمة
- نظام اختبارات تفاعلي شامل
- تتبع التقدم الشخصي للطلاب
- منتدى للنقاش والأسئلة العلمية
- تطبيق محمول مصاحب للمنصة
- محتوى فيديو تعليمي متقدم

#### التحسينات المستقبلية
- إضافة المزيد من المحاكيات المتقدمة
- تطوير نظام تقييم ذكي
- دمج تقنيات الواقع المعزز
- توسيع المحتوى ليشمل العلاج الإشعاعي
- إضافة حالات سريرية أكثر تعقيداً

### 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

### 🤝 المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

### 📞 التواصل
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.nuclear-imaging-physics.edu

---
© 2024 منصة التصوير الطبي النووي. جميع الحقوق محفوظة.
