// Interactive Tools and Advanced UI Components
// الأدوات التفاعلية والمكونات المتقدمة

class InteractiveTools {
    constructor() {
        this.tools = new Map();
        this.activeTools = new Set();
    }

    // أداة حاسبة الجرعة الإشعاعية
    createDoseCalculator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const calculator = document.createElement('div');
        calculator.className = 'dose-calculator';
        calculator.innerHTML = `
            <div class="calculator-header">
                <h3><i class="fas fa-calculator"></i> حاسبة الجرعة الإشعاعية</h3>
            </div>
            <div class="calculator-body">
                <div class="input-group">
                    <label>النشاط (MBq):</label>
                    <input type="number" id="activity" value="370" min="1" max="10000">
                </div>
                <div class="input-group">
                    <label>الوقت (دقيقة):</label>
                    <input type="number" id="time" value="30" min="1" max="1440">
                </div>
                <div class="input-group">
                    <label>المسافة (متر):</label>
                    <input type="number" id="distance" value="1" min="0.1" max="10" step="0.1">
                </div>
                <div class="input-group">
                    <label>النظير المشع:</label>
                    <select id="isotope">
                        <option value="tc99m">Tc-99m</option>
                        <option value="i123">I-123</option>
                        <option value="f18">F-18</option>
                        <option value="in111">In-111</option>
                    </select>
                </div>
                <button class="calc-btn" onclick="calculateDose()">
                    <i class="fas fa-play"></i> حساب الجرعة
                </button>
                <div class="result-panel" id="dose-result">
                    <div class="result-item">
                        <span class="result-label">معدل الجرعة:</span>
                        <span class="result-value" id="dose-rate">-- μSv/h</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">الجرعة الإجمالية:</span>
                        <span class="result-value" id="total-dose">-- μSv</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">التقييم:</span>
                        <span class="result-value" id="dose-assessment">--</span>
                    </div>
                </div>
            </div>
        `;

        // إضافة الأنماط
        const style = document.createElement('style');
        style.textContent = `
            .dose-calculator {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 15px;
                padding: 2rem;
                color: white;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                max-width: 400px;
                margin: 0 auto;
            }

            .calculator-header h3 {
                margin: 0 0 1.5rem 0;
                text-align: center;
                font-size: 1.3rem;
            }

            .input-group {
                margin-bottom: 1rem;
            }

            .input-group label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            .input-group input,
            .input-group select {
                width: 100%;
                padding: 0.75rem;
                border: none;
                border-radius: 8px;
                font-size: 1rem;
                background: rgba(255, 255, 255, 0.9);
                color: #333;
            }

            .calc-btn {
                width: 100%;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                border: none;
                padding: 1rem;
                border-radius: 10px;
                font-size: 1.1rem;
                font-weight: 600;
                cursor: pointer;
                margin: 1rem 0;
                transition: all 0.3s ease;
            }

            .calc-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
            }

            .result-panel {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 1rem;
                backdrop-filter: blur(10px);
            }

            .result-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.5rem;
                padding: 0.5rem 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }

            .result-item:last-child {
                border-bottom: none;
            }

            .result-label {
                font-weight: 600;
            }

            .result-value {
                font-weight: 700;
                color: #ffd93d;
            }
        `;

        document.head.appendChild(style);
        container.appendChild(calculator);

        // دالة حساب الجرعة
        window.calculateDose = function() {
            const activity = parseFloat(document.getElementById('activity').value);
            const time = parseFloat(document.getElementById('time').value);
            const distance = parseFloat(document.getElementById('distance').value);
            const isotope = document.getElementById('isotope').value;

            // ثوابت النظائر (Γ constant in μSv·m²/MBq·h)
            const gammaConstants = {
                'tc99m': 0.019,
                'i123': 0.041,
                'f18': 0.146,
                'in111': 0.067
            };

            const gamma = gammaConstants[isotope];
            const doseRate = (gamma * activity) / (distance * distance);
            const totalDose = doseRate * (time / 60);

            document.getElementById('dose-rate').textContent = doseRate.toFixed(2) + ' μSv/h';
            document.getElementById('total-dose').textContent = totalDose.toFixed(2) + ' μSv';

            // تقييم الجرعة
            let assessment = '';
            if (totalDose < 1) {
                assessment = 'منخفضة جداً';
            } else if (totalDose < 10) {
                assessment = 'منخفضة';
            } else if (totalDose < 100) {
                assessment = 'متوسطة';
            } else {
                assessment = 'عالية - احتياطات مطلوبة';
            }

            document.getElementById('dose-assessment').textContent = assessment;
        };

        return calculator;
    }

    // أداة محلل الطيف التفاعلي
    createSpectrumAnalyzer(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const analyzer = document.createElement('div');
        analyzer.className = 'spectrum-analyzer';
        analyzer.innerHTML = `
            <div class="analyzer-header">
                <h3><i class="fas fa-chart-line"></i> محلل الطيف التفاعلي</h3>
            </div>
            <div class="analyzer-controls">
                <button class="control-btn" onclick="loadSpectrum('tc99m')">Tc-99m</button>
                <button class="control-btn" onclick="loadSpectrum('i123')">I-123</button>
                <button class="control-btn" onclick="loadSpectrum('f18')">F-18</button>
                <button class="control-btn" onclick="addNoise()">إضافة ضوضاء</button>
                <button class="control-btn" onclick="smoothSpectrum()">تنعيم</button>
            </div>
            <canvas id="spectrum-canvas" width="600" height="400"></canvas>
            <div class="spectrum-info" id="spectrum-info">
                <div class="info-item">
                    <span>الطاقة:</span>
                    <span id="cursor-energy">-- keV</span>
                </div>
                <div class="info-item">
                    <span>العدد:</span>
                    <span id="cursor-counts">--</span>
                </div>
                <div class="info-item">
                    <span>FWHM:</span>
                    <span id="spectrum-fwhm">-- keV</span>
                </div>
            </div>
        `;

        const canvas = analyzer.querySelector('#spectrum-canvas');
        const ctx = canvas.getContext('2d');
        let currentSpectrum = [];
        let mouseX = 0;

        // إضافة الأنماط
        const style = document.createElement('style');
        style.textContent = `
            .spectrum-analyzer {
                background: white;
                border-radius: 15px;
                padding: 1.5rem;
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                max-width: 650px;
                margin: 0 auto;
            }

            .analyzer-header h3 {
                margin: 0 0 1rem 0;
                color: #1f2937;
                text-align: center;
            }

            .analyzer-controls {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-bottom: 1rem;
                justify-content: center;
            }

            .control-btn {
                background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: all 0.3s ease;
            }

            .control-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            #spectrum-canvas {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                cursor: crosshair;
                width: 100%;
                height: auto;
            }

            .spectrum-info {
                display: flex;
                justify-content: space-around;
                margin-top: 1rem;
                padding: 1rem;
                background: #f8fafc;
                border-radius: 8px;
            }

            .info-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.25rem;
            }

            .info-item span:first-child {
                font-weight: 600;
                color: #6b7280;
                font-size: 0.9rem;
            }

            .info-item span:last-child {
                font-weight: 700;
                color: #1f2937;
                font-size: 1.1rem;
            }
        `;

        document.head.appendChild(style);

        // تفاعل الماوس
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = e.clientX - rect.left;
            updateCursorInfo();
        });

        // دوال التحكم
        window.loadSpectrum = function(isotope) {
            const spectra = {
                'tc99m': generateGaussianSpectrum(140, 14, 1000),
                'i123': generateGaussianSpectrum(159, 16, 800),
                'f18': generateGaussianSpectrum(511, 25, 1200)
            };
            
            currentSpectrum = spectra[isotope] || [];
            drawSpectrum();
        };

        window.addNoise = function() {
            currentSpectrum = currentSpectrum.map(value => 
                value + (Math.random() - 0.5) * value * 0.1
            );
            drawSpectrum();
        };

        window.smoothSpectrum = function() {
            const smoothed = [];
            for (let i = 0; i < currentSpectrum.length; i++) {
                let sum = 0;
                let count = 0;
                for (let j = Math.max(0, i-2); j <= Math.min(currentSpectrum.length-1, i+2); j++) {
                    sum += currentSpectrum[j];
                    count++;
                }
                smoothed[i] = sum / count;
            }
            currentSpectrum = smoothed;
            drawSpectrum();
        };

        function generateGaussianSpectrum(peak, fwhm, amplitude) {
            const spectrum = [];
            const sigma = fwhm / 2.35;
            
            for (let i = 0; i < 1000; i++) {
                const energy = i * 0.5; // keV
                const value = amplitude * Math.exp(-0.5 * Math.pow((energy - peak) / sigma, 2));
                spectrum.push(value + Math.random() * amplitude * 0.05);
            }
            
            return spectrum;
        }

        function drawSpectrum() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (currentSpectrum.length === 0) return;

            // رسم الشبكة
            ctx.strokeStyle = '#f3f4f6';
            ctx.lineWidth = 1;
            
            for (let i = 0; i <= 10; i++) {
                const x = (i / 10) * canvas.width;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
                
                const y = (i / 10) * canvas.height;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // رسم الطيف
            const maxValue = Math.max(...currentSpectrum);
            
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            currentSpectrum.forEach((value, index) => {
                const x = (index / currentSpectrum.length) * canvas.width;
                const y = canvas.height - (value / maxValue) * canvas.height;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();

            // ملء المنطقة
            ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
            ctx.fill();
        }

        function updateCursorInfo() {
            if (currentSpectrum.length === 0) return;
            
            const energy = (mouseX / canvas.width) * 500; // 500 keV max
            const index = Math.floor((mouseX / canvas.width) * currentSpectrum.length);
            const counts = currentSpectrum[index] || 0;
            
            document.getElementById('cursor-energy').textContent = energy.toFixed(1) + ' keV';
            document.getElementById('cursor-counts').textContent = Math.round(counts);
        }

        container.appendChild(analyzer);

        // تحميل طيف افتراضي
        loadSpectrum('tc99m');

        return analyzer;
    }

    // أداة محاكي الحماية الإشعاعية
    createShieldingSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const simulator = document.createElement('div');
        simulator.className = 'shielding-simulator';
        simulator.innerHTML = `
            <div class="simulator-header">
                <h3><i class="fas fa-shield-alt"></i> محاكي الحماية الإشعاعية</h3>
            </div>
            <div class="simulator-controls">
                <div class="control-group">
                    <label>المادة:</label>
                    <select id="shield-material">
                        <option value="lead">رصاص</option>
                        <option value="concrete">خرسانة</option>
                        <option value="steel">فولاذ</option>
                        <option value="aluminum">ألومنيوم</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>السماكة (mm):</label>
                    <input type="range" id="thickness" min="0" max="50" value="10" oninput="updateShielding()">
                    <span id="thickness-value">10 mm</span>
                </div>
                <div class="control-group">
                    <label>طاقة الفوتون (keV):</label>
                    <input type="range" id="photon-energy" min="50" max="500" value="140" oninput="updateShielding()">
                    <span id="energy-value">140 keV</span>
                </div>
            </div>
            <canvas id="shielding-canvas" width="500" height="300"></canvas>
            <div class="shielding-results">
                <div class="result-item">
                    <span>معامل التوهين:</span>
                    <span id="attenuation-coeff">-- cm⁻¹</span>
                </div>
                <div class="result-item">
                    <span>النفاذية:</span>
                    <span id="transmission">-- %</span>
                </div>
                <div class="result-item">
                    <span>طبقة النصف:</span>
                    <span id="half-value-layer">-- mm</span>
                </div>
            </div>
        `;

        const canvas = simulator.querySelector('#shielding-canvas');
        const ctx = canvas.getContext('2d');

        // معاملات التوهين (cm⁻¹) للمواد المختلفة عند طاقات مختلفة
        const attenuationData = {
            lead: { 100: 59.7, 140: 26.4, 200: 13.8, 300: 7.6, 500: 4.8 },
            concrete: { 100: 0.16, 140: 0.15, 200: 0.14, 300: 0.13, 500: 0.12 },
            steel: { 100: 0.43, 140: 0.38, 200: 0.32, 300: 0.27, 500: 0.23 },
            aluminum: { 100: 0.16, 140: 0.15, 200: 0.14, 300: 0.13, 500: 0.12 }
        };

        // إضافة الأنماط
        const style = document.createElement('style');
        style.textContent = `
            .shielding-simulator {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                border-radius: 15px;
                padding: 2rem;
                color: white;
                max-width: 550px;
                margin: 0 auto;
            }

            .simulator-header h3 {
                margin: 0 0 1.5rem 0;
                text-align: center;
            }

            .simulator-controls {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .control-group {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .control-group label {
                font-weight: 600;
                font-size: 0.9rem;
            }

            .control-group select,
            .control-group input[type="range"] {
                padding: 0.5rem;
                border: none;
                border-radius: 5px;
                background: rgba(255, 255, 255, 0.9);
            }

            #shielding-canvas {
                background: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                width: 100%;
                height: auto;
                margin-bottom: 1rem;
            }

            .shielding-results {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
                background: rgba(255, 255, 255, 0.1);
                padding: 1rem;
                border-radius: 10px;
            }

            .result-item {
                text-align: center;
            }

            .result-item span:first-child {
                display: block;
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
            }

            .result-item span:last-child {
                display: block;
                font-weight: 700;
                font-size: 1.1rem;
                color: #ffd93d;
            }
        `;

        document.head.appendChild(style);

        // دالة تحديث المحاكاة
        window.updateShielding = function() {
            const material = document.getElementById('shield-material').value;
            const thickness = parseFloat(document.getElementById('thickness').value) / 10; // تحويل إلى cm
            const energy = parseFloat(document.getElementById('photon-energy').value);

            document.getElementById('thickness-value').textContent = (thickness * 10) + ' mm';
            document.getElementById('energy-value').textContent = energy + ' keV';

            // حساب معامل التوهين بالاستيفاء
            const mu = interpolateAttenuation(material, energy);
            const transmission = Math.exp(-mu * thickness) * 100;
            const hvl = Math.log(2) / mu * 10; // mm

            document.getElementById('attenuation-coeff').textContent = mu.toFixed(3) + ' cm⁻¹';
            document.getElementById('transmission').textContent = transmission.toFixed(1) + ' %';
            document.getElementById('half-value-layer').textContent = hvl.toFixed(1) + ' mm';

            drawShielding(thickness * 10, transmission);
        };

        function interpolateAttenuation(material, energy) {
            const data = attenuationData[material];
            const energies = Object.keys(data).map(Number).sort((a, b) => a - b);
            
            if (energy <= energies[0]) return data[energies[0]];
            if (energy >= energies[energies.length - 1]) return data[energies[energies.length - 1]];
            
            for (let i = 0; i < energies.length - 1; i++) {
                if (energy >= energies[i] && energy <= energies[i + 1]) {
                    const x1 = energies[i];
                    const x2 = energies[i + 1];
                    const y1 = data[x1];
                    const y2 = data[x2];
                    
                    return y1 + (y2 - y1) * (energy - x1) / (x2 - x1);
                }
            }
            
            return data[energies[0]];
        }

        function drawShielding(thickness, transmission) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // رسم المصدر
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(50, canvas.height / 2, 20, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('مصدر', 30, canvas.height / 2 + 35);

            // رسم الأشعة قبل الحماية
            for (let i = 0; i < 10; i++) {
                const y = canvas.height / 2 - 50 + i * 10;
                ctx.strokeStyle = '#ffff00';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(70, y);
                ctx.lineTo(200, y);
                ctx.stroke();
            }

            // رسم الحماية
            const shieldX = 200;
            const shieldWidth = Math.max(5, thickness * 2); // تكبير للعرض
            
            ctx.fillStyle = '#666';
            ctx.fillRect(shieldX, canvas.height / 2 - 60, shieldWidth, 120);
            
            ctx.fillStyle = '#333';
            ctx.fillText(`${thickness} mm`, shieldX, canvas.height / 2 + 80);

            // رسم الأشعة بعد الحماية
            const attenuatedRays = Math.round(10 * transmission / 100);
            for (let i = 0; i < attenuatedRays; i++) {
                const y = canvas.height / 2 - 50 + i * 10;
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(shieldX + shieldWidth, y);
                ctx.lineTo(450, y);
                ctx.stroke();
            }

            // رسم الكاشف
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(450, canvas.height / 2 - 40, 20, 80);
            
            ctx.fillStyle = '#333';
            ctx.fillText('كاشف', 440, canvas.height / 2 + 60);

            // عرض النسبة المئوية
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText(`${transmission.toFixed(1)}%`, 350, canvas.height / 2);
        }

        container.appendChild(simulator);

        // تهيئة المحاكاة
        updateShielding();

        return simulator;
    }

    // أداة مخطط الاضمحلال التفاعلي
    createDecayChainVisualization(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const visualization = document.createElement('div');
        visualization.className = 'decay-chain-viz';
        visualization.innerHTML = `
            <div class="viz-header">
                <h3><i class="fas fa-project-diagram"></i> مخطط الاضمحلال التفاعلي</h3>
            </div>
            <div class="chain-controls">
                <button class="chain-btn" onclick="loadDecayChain('u238')">U-238</button>
                <button class="chain-btn" onclick="loadDecayChain('th232')">Th-232</button>
                <button class="chain-btn" onclick="loadDecayChain('mo99')">Mo-99</button>
                <button class="chain-btn" onclick="animateDecay()">تحريك الاضمحلال</button>
            </div>
            <svg id="decay-chain-svg" width="700" height="400"></svg>
            <div class="chain-info" id="chain-info">
                <div class="info-panel">
                    <h4>معلومات النظير</h4>
                    <div id="isotope-details">اختر نظيراً لعرض التفاصيل</div>
                </div>
            </div>
        `;

        const svg = visualization.querySelector('#decay-chain-svg');

        // بيانات سلاسل الاضمحلال
        const decayChains = {
            u238: [
                { name: 'U-238', halfLife: '4.5 مليار سنة', type: 'α', x: 100, y: 50 },
                { name: 'Th-234', halfLife: '24 يوم', type: 'β⁻', x: 250, y: 50 },
                { name: 'Pa-234', halfLife: '1.2 دقيقة', type: 'β⁻', x: 400, y: 50 },
                { name: 'U-234', halfLife: '245000 سنة', type: 'α', x: 550, y: 50 },
                { name: 'Th-230', halfLife: '75000 سنة', type: 'α', x: 550, y: 150 },
                { name: 'Ra-226', halfLife: '1600 سنة', type: 'α', x: 400, y: 150 },
                { name: 'Rn-222', halfLife: '3.8 يوم', type: 'α', x: 250, y: 150 },
                { name: 'Po-218', halfLife: '3.1 دقيقة', type: 'α', x: 100, y: 150 }
            ],
            mo99: [
                { name: 'Mo-99', halfLife: '66 ساعة', type: 'β⁻', x: 200, y: 100 },
                { name: 'Tc-99m', halfLife: '6 ساعة', type: 'IT', x: 400, y: 100 },
                { name: 'Tc-99', halfLife: '211000 سنة', type: 'β⁻', x: 600, y: 100 }
            ]
        };

        // إضافة الأنماط
        const style = document.createElement('style');
        style.textContent = `
            .decay-chain-viz {
                background: white;
                border-radius: 15px;
                padding: 2rem;
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                max-width: 750px;
                margin: 0 auto;
            }

            .viz-header h3 {
                margin: 0 0 1rem 0;
                color: #1f2937;
                text-align: center;
            }

            .chain-controls {
                display: flex;
                justify-content: center;
                gap: 0.5rem;
                margin-bottom: 1rem;
                flex-wrap: wrap;
            }

            .chain-btn {
                background: linear-gradient(135deg, #8b5cf6, #7c3aed);
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: all 0.3s ease;
            }

            .chain-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }

            #decay-chain-svg {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                background: #f8fafc;
                width: 100%;
                height: auto;
            }

            .chain-info {
                margin-top: 1rem;
            }

            .info-panel {
                background: #f8fafc;
                padding: 1rem;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
            }

            .info-panel h4 {
                margin: 0 0 0.5rem 0;
                color: #1f2937;
            }

            .isotope-node {
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .isotope-node:hover {
                transform: scale(1.1);
            }

            .decay-arrow {
                marker-end: url(#arrowhead);
                stroke: #6b7280;
                stroke-width: 2;
                fill: none;
            }
        `;

        document.head.appendChild(style);

        // دوال التحكم
        window.loadDecayChain = function(chainType) {
            const chain = decayChains[chainType];
            if (!chain) return;

            // مسح SVG
            svg.innerHTML = '';

            // إضافة تعريف السهم
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            marker.setAttribute('id', 'arrowhead');
            marker.setAttribute('markerWidth', '10');
            marker.setAttribute('markerHeight', '7');
            marker.setAttribute('refX', '9');
            marker.setAttribute('refY', '3.5');
            marker.setAttribute('orient', 'auto');
            
            const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
            polygon.setAttribute('fill', '#6b7280');
            
            marker.appendChild(polygon);
            defs.appendChild(marker);
            svg.appendChild(defs);

            // رسم النظائر
            chain.forEach((isotope, index) => {
                // رسم العقدة
                const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                group.setAttribute('class', 'isotope-node');
                
                const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                circle.setAttribute('cx', isotope.x);
                circle.setAttribute('cy', isotope.y);
                circle.setAttribute('r', '30');
                circle.setAttribute('fill', getIsotopeColor(isotope.type));
                circle.setAttribute('stroke', '#333');
                circle.setAttribute('stroke-width', '2');
                
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', isotope.x);
                text.setAttribute('y', isotope.y);
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('dominant-baseline', 'middle');
                text.setAttribute('font-family', 'Cairo');
                text.setAttribute('font-size', '12');
                text.setAttribute('font-weight', 'bold');
                text.setAttribute('fill', 'white');
                text.textContent = isotope.name;
                
                // معلومات إضافية
                const halfLifeText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                halfLifeText.setAttribute('x', isotope.x);
                halfLifeText.setAttribute('y', isotope.y + 50);
                halfLifeText.setAttribute('text-anchor', 'middle');
                halfLifeText.setAttribute('font-family', 'Cairo');
                halfLifeText.setAttribute('font-size', '10');
                halfLifeText.setAttribute('fill', '#6b7280');
                halfLifeText.textContent = isotope.halfLife;
                
                group.appendChild(circle);
                group.appendChild(text);
                group.appendChild(halfLifeText);
                
                // إضافة تفاعل
                group.addEventListener('click', () => showIsotopeDetails(isotope));
                
                svg.appendChild(group);

                // رسم السهم للنظير التالي
                if (index < chain.length - 1) {
                    const nextIsotope = chain[index + 1];
                    const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    arrow.setAttribute('class', 'decay-arrow');
                    arrow.setAttribute('x1', isotope.x + 30);
                    arrow.setAttribute('y1', isotope.y);
                    arrow.setAttribute('x2', nextIsotope.x - 30);
                    arrow.setAttribute('y2', nextIsotope.y);
                    
                    svg.appendChild(arrow);

                    // تسمية نوع الاضمحلال
                    const decayLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                    decayLabel.setAttribute('x', (isotope.x + nextIsotope.x) / 2);
                    decayLabel.setAttribute('y', (isotope.y + nextIsotope.y) / 2 - 10);
                    decayLabel.setAttribute('text-anchor', 'middle');
                    decayLabel.setAttribute('font-family', 'Cairo');
                    decayLabel.setAttribute('font-size', '12');
                    decayLabel.setAttribute('font-weight', 'bold');
                    decayLabel.setAttribute('fill', '#ef4444');
                    decayLabel.textContent = isotope.type;
                    
                    svg.appendChild(decayLabel);
                }
            });
        };

        function getIsotopeColor(decayType) {
            const colors = {
                'α': '#ef4444',
                'β⁻': '#3b82f6',
                'β⁺': '#10b981',
                'IT': '#f59e0b',
                'EC': '#8b5cf6'
            };
            return colors[decayType] || '#6b7280';
        }

        function showIsotopeDetails(isotope) {
            const details = `
                <strong>${isotope.name}</strong><br>
                عمر النصف: ${isotope.halfLife}<br>
                نوع الاضمحلال: ${isotope.type}<br>
                الاستخدام: ${getIsotopeUse(isotope.name)}
            `;
            document.getElementById('isotope-details').innerHTML = details;
        }

        function getIsotopeUse(name) {
            const uses = {
                'Tc-99m': 'التصوير الطبي النووي',
                'Mo-99': 'مولد Tc-99m',
                'I-123': 'تصوير الغدة الدرقية',
                'F-18': 'PET imaging'
            };
            return uses[name] || 'استخدامات متنوعة';
        }

        window.animateDecay = function() {
            // إضافة رسوم متحركة للاضمحلال
            const nodes = svg.querySelectorAll('.isotope-node');
            nodes.forEach((node, index) => {
                setTimeout(() => {
                    node.style.animation = 'pulse 1s ease-in-out';
                }, index * 500);
            });
        };

        container.appendChild(visualization);

        // تحميل سلسلة افتراضية
        loadDecayChain('mo99');

        return visualization;
    }
}

// إنشاء مثيل عام للاستخدام
const interactiveTools = new InteractiveTools();

// دوال مساعدة للاستخدام السهل
function createDoseCalculator(containerId, options = {}) {
    return interactiveTools.createDoseCalculator(containerId, options);
}

function createSpectrumAnalyzer(containerId, options = {}) {
    return interactiveTools.createSpectrumAnalyzer(containerId, options);
}

function createShieldingSimulator(containerId, options = {}) {
    return interactiveTools.createShieldingSimulator(containerId, options);
}

function createDecayChainVisualization(containerId, options = {}) {
    return interactiveTools.createDecayChainVisualization(containerId, options);
}