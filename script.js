// DOM Elements
const navMenu = document.getElementById('nav-menu');
const navToggle = document.getElementById('nav-toggle');
const navLinks = document.querySelectorAll('.nav-link');
const chapterModal = document.getElementById('chapterModal');
const modalTitle = document.getElementById('modalTitle');
const modalBody = document.getElementById('modalBody');
const loadingOverlay = document.getElementById('loadingOverlay');

// Chapter Data
const chaptersData = [
    {
        id: 1,
        title: "مقدمة في الطب النووي",
        subtitle: "التاريخ والمبادئ الأساسية",
        content: `
            <div class="chapter-section">
                <h2>التطور التاريخي للطب النووي</h2>
                <p>تطور الطب النووي من اكتشافات مبكرة في النشاط الإشعاعي ليصبح تخصصًا طبيًا متطورًا للتصوير والعلاج.</p>
                
                <h3>المعالم التاريخية الرئيسية</h3>
                <ul>
                    <li><strong>1896</strong>: اكتشف هنري بيكريل النشاط الإشعاعي</li>
                    <li><strong>1898</strong>: عزلت ماري وبيير كوري الراديوم والبولونيوم</li>
                    <li><strong>1913</strong>: قدم جورج دي هيفيسي مفهوم المقتفيات المشعة</li>
                    <li><strong>1958</strong>: طور هال أنجر كاميرا جاما</li>
                    <li><strong>1970s</strong>: تطوير تصوير SPECT</li>
                    <li><strong>1975</strong>: تطوير أول ماسح PET</li>
                </ul>
                
                <div class="chapter-figure">
                    <div class="figure-placeholder">
                        <i class="fas fa-image"></i>
                        <span>الشكل 1-1: الخط الزمني لتطور الطب النووي</span>
                    </div>
                    <p class="figure-caption">الشكل 1-1: يوضح المعالم الرئيسية في تطور الطب النووي من اكتشاف النشاط الإشعاعي إلى أنظمة التصوير الحديثة.</p>
                </div>
            </div>
            
            <div class="chapter-section">
                <h2>المبادئ الأساسية</h2>
                <p>يعتمد الطب النووي على إعطاء مركبات مشعة (مستحضرات صيدلانية إشعاعية) تنبعث منها إشعاعات يمكن كشفها بواسطة أجهزة تصوير خارجية.</p>
                
                <h3>الاضمحلال الإشعاعي</h3>
                <p>التحول التلقائي للنوى الذرية غير المستقرة، ويوصف بمعادلة الاضمحلال:</p>
                
                <div class="equation">
                    <span>N(t) = N₀ e<sup>-λt</sup></span>
                </div>
                
                <p>حيث:</p>
                <ul>
                    <li>N(t) = عدد النوى المشعة عند الزمن t</li>
                    <li>N₀ = العدد الأولي للنوى المشعة</li>
                    <li>λ = ثابت الاضمحلال</li>
                    <li>t = الزمن</li>
                </ul>
                
                <h3>عمر النصف</h3>
                <p>الزمن اللازم لاضمحلال نصف النوى المشعة:</p>
                
                <div class="equation">
                    <span>t<sub>1/2</sub> = ln(2)/λ = 0.693/λ</span>
                </div>
                
                <h3>أنواع الإشعاع</h3>
                <ul>
                    <li><strong>جسيمات ألفا (α)</strong>: نوى الهيليوم، طاقة عالية، اختراق منخفض</li>
                    <li><strong>جسيمات بيتا (β)</strong>: إلكترونات أو بوزيترونات، تستخدم في التصوير والعلاج</li>
                    <li><strong>أشعة جاما (γ)</strong>: إشعاع كهرومغناطيسي، اختراق عالي، مثالي للتصوير</li>
                </ul>
                
                <div class="interactive-element">
                    <h4>محاكي الاضمحلال الإشعاعي</h4>
                    <div class="interactive-placeholder">
                        <i class="fas fa-atom"></i>
                        <span>انقر للتفاعل مع محاكي الاضمحلال الإشعاعي</span>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 2,
        title: "الفيزياء الذرية والنووية",
        subtitle: "البنية والنشاط الإشعاعي",
        content: `
            <div class="chapter-section">
                <h2>بنية الذرة والنواة</h2>
                <p>فهم بنية الذرة أساسي لفيزياء الطب النووي.</p>
                
                <h3>مكونات الذرة</h3>
                <ul>
                    <li><strong>النواة</strong>: تحتوي على بروتونات (شحنة موجبة) ونيوترونات (متعادلة)</li>
                    <li><strong>الإلكترونات</strong>: تدور حول النواة في مستويات طاقة محددة</li>
                </ul>
                
                <div class="chapter-figure">
                    <div class="figure-placeholder">
                        <i class="fas fa-atom"></i>
                        <span>الشكل 2-1: بنية الذرة</span>
                    </div>
                    <p class="figure-caption">الشكل 2-1: رسم تخطيطي لبنية الذرة يوضح النواة والإلكترونات.</p>
                </div>
                
                <h3>الترميز النووي</h3>
                <p>يتم تمثيل النوى كالتالي: <sup>A</sup><sub>Z</sub>X</p>
                <ul>
                    <li>A = العدد الكتلي (البروتونات + النيوترونات)</li>
                    <li>Z = العدد الذري (البروتونات)</li>
                    <li>X = الرمز الكيميائي</li>
                </ul>
                
                <h3>طاقة الربط النووية</h3>
                <p>الطاقة اللازمة لفصل النواة إلى نيوكليونات فردية:</p>
                
                <div class="equation">
                    <span>BE = (Z·m<sub>p</sub> + N·m<sub>n</sub> - M<sub>nucleus</sub>)·c<sup>2</sup></span>
                </div>
            </div>
            
            <div class="chapter-section">
                <h2>أنماط الاضمحلال الإشعاعي</h2>
                
                <h3>اضمحلال ألفا (α)</h3>
                <p>النوى الثقيلة تصدر جسيمات ألفا:</p>
                
                <div class="equation">
                    <span><sup>A</sup><sub>Z</sub>X → <sup>A-4</sup><sub>Z-2</sub>Y + <sup>4</sup><sub>2</sub>He</span>
                </div>
                
                <h3>اضمحلال بيتا السالب (β<sup>-</sup>)</h3>
                <p>النوى الغنية بالنيوترونات تحول نيوترون إلى بروتون:</p>
                
                <div class="equation">
                    <span>n → p + e<sup>-</sup> + ν̄<sub>e</sub></span>
                </div>
                
                <h3>اضمحلال بيتا الموجب (β<sup>+</sup>)</h3>
                <p>النوى الغنية بالبروتونات تحول بروتون إلى نيوترون:</p>
                
                <div class="equation">
                    <span>p → n + e<sup>+</sup> + ν<sub>e</sub></span>
                </div>
                
                <p>عملية الفناء:</p>
                <div class="equation">
                    <span>e<sup>+</sup> + e<sup>-</sup> → 2γ (511 keV)</span>
                </div>
                
                <div class="interactive-element">
                    <h4>محاكي أنماط الاضمحلال</h4>
                    <div class="interactive-placeholder">
                        <i class="fas fa-radiation"></i>
                        <span>انقر للتفاعل مع محاكي أنماط الاضمحلال</span>
                    </div>
                </div>
            </div>
        `
    }
];

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Mobile Navigation
    navToggle.addEventListener('click', () => {
        navToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
    
    // Close mobile menu when clicking a link
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Initialize KaTeX for math rendering
    if (typeof renderMathInElement === 'function') {
        renderMathInElement(document.body, {
            delimiters: [
                {left: "$$", right: "$$", display: true},
                {left: "$", right: "$", display: false}
            ]
        });
    }
    
    // Initialize interactive demos
    initializeInteractiveDemos();
});

// Functions
function startLearning() {
    window.location.href = '#chapters';
}

function showDemo() {
    // Show a demo of interactive elements
    window.location.href = '#interactive';
}

function openChapter(chapterId) {
    showLoading();
    
    // Find the chapter data
    const chapter = chaptersData.find(ch => ch.id === chapterId);
    
    if (chapter) {
        modalTitle.textContent = `الفصل ${chapter.id}: ${chapter.title}`;
        modalBody.innerHTML = chapter.content;
        
        // Initialize KaTeX in the modal
        if (typeof renderMathInElement === 'function') {
            renderMathInElement(modalBody, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ]
            });
        }
        
        // Show the modal
        chapterModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
    
    hideLoading();
}

function closeModal() {
    chapterModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function showLoading() {
    loadingOverlay.style.display = 'block';
}

function hideLoading() {
    setTimeout(() => {
        loadingOverlay.style.display = 'none';
    }, 500);
}

// Close modal when clicking outside
window.onclick = function(event) {
    if (event.target === chapterModal) {
        closeModal();
    }
};

// Interactive Demos
function initializeInteractiveDemos() {
    // Decay Demo
    const decayDemo = document.getElementById('decay-demo');
    if (decayDemo) {
        const nuclei = decayDemo.querySelectorAll('.nucleus.active');
        let isPlaying = false;
        
        decayDemo.querySelector('.demo-btn').addEventListener('click', () => {
            toggleDecayDemo();
        });
        
        function toggleDecayDemo() {
            isPlaying = !isPlaying;
            
            if (isPlaying) {
                decayDemo.querySelector('.demo-btn i').className = 'fas fa-pause';
                simulateDecay();
            } else {
                decayDemo.querySelector('.demo-btn i').className = 'fas fa-play';
            }
        }
        
        function simulateDecay() {
            if (!isPlaying) return;
            
            const activeNuclei = decayDemo.querySelectorAll('.nucleus.active');
            if (activeNuclei.length > 0) {
                const randomIndex = Math.floor(Math.random() * activeNuclei.length);
                activeNuclei[randomIndex].classList.remove('active');
                activeNuclei[randomIndex].classList.add('decayed');
                
                setTimeout(simulateDecay, 1000);
            } else {
                isPlaying = false;
                decayDemo.querySelector('.demo-btn i').className = 'fas fa-play';
            }
        }
    }
    
    // Calculator Demo
    const calculatorDemo = document.getElementById('calculator-demo');
    if (calculatorDemo) {
        const slider = calculatorDemo.querySelector('.calc-slider');
        const valueDisplay = calculatorDemo.querySelector('.calc-value');
        
        slider.addEventListener('input', () => {
            valueDisplay.textContent = `${slider.value} Bq`;
        });
    }
}

// Form Submission
const contactForm = document.getElementById('contactForm');
if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        showLoading();
        
        // Simulate form submission
        setTimeout(() => {
            alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريبًا.');
            contactForm.reset();
            hideLoading();
        }, 1500);
    });
}

// Additional Functions
function showGlossary() {
    alert('سيتم إضافة قاموس المصطلحات قريبًا!');
}

function showReferences() {
    alert('سيتم إضافة قائمة المراجع العلمية قريبًا!');
}

function downloadPDF() {
    alert('سيتم إتاحة تحميل نسخة PDF قريبًا!');
}

function showHelp() {
    alert('سيتم إضافة صفحة المساعدة قريبًا!');
}

function showFAQ() {
    alert('سيتم إضافة صفحة الأسئلة الشائعة قريبًا!');
}

function reportIssue() {
    alert('سيتم إضافة نموذج الإبلاغ عن مشكلة قريبًا!');
}

function showPrivacy() {
    alert('سيتم إضافة سياسة الخصوصية قريبًا!');
}

function showTerms() {
    alert('سيتم إضافة شروط الاستخدام قريبًا!');
}

function showLicense() {
    alert('سيتم إضافة معلومات الترخيص قريبًا!');
}
