/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: #ffffff;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header and Navigation */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 2rem;
    animation: rotate 10s linear infinite;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.highlight {
    color: #fbbf24;
    display: block;
    font-size: 2.5rem;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background-color: #fbbf24;
    color: var(--dark-color);
}

.btn-primary:hover {
    background-color: #f59e0b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: var(--primary-color);
}

/* Hero Visual - Atom Animation */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.atom-animation {
    position: relative;
    width: 300px;
    height: 300px;
}

.nucleus {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #fbbf24, #f59e0b);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.electron-orbit {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.orbit-1 {
    width: 100px;
    height: 100px;
    animation: rotate 4s linear infinite;
}

.orbit-2 {
    width: 160px;
    height: 160px;
    animation: rotate 6s linear infinite reverse;
}

.orbit-3 {
    width: 220px;
    height: 220px;
    animation: rotate 8s linear infinite;
}

.electron {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #60a5fa;
    border-radius: 50%;
    top: -4px;
    right: -4px;
    box-shadow: 0 0 10px rgba(96, 165, 250, 0.7);
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: var(--light-color);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Chapters Section */
.chapters {
    padding: 80px 0;
    background-color: white;
}

.chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.chapter-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-color);
}

.chapter-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.chapter-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.chapter-number {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.chapter-info {
    flex: 1;
}

.chapter-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.chapter-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.chapter-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.duration {
    font-size: 0.85rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.difficulty {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.beginner {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.intermediate {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.advanced {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.chapter-content {
    padding: 1.5rem;
}

.chapter-topics {
    list-style: none;
    margin-bottom: 1.5rem;
}

.chapter-topics li {
    position: relative;
    padding-right: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.chapter-topics li::before {
    content: "";
    position: absolute;
    right: 0;
    top: 0.5rem;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.chapter-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.chapter-btn {
    width: 100%;
    padding: 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.chapter-btn:hover {
    background-color: var(--secondary-color);
}

.coming-soon {
    position: relative;
}

.coming-soon-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(31, 41, 55, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.coming-soon-overlay i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

/* Interactive Section */
.interactive-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.interactive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.interactive-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
}

.interactive-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.interactive-demo {
    height: 200px;
    background-color: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--border-color);
    overflow: hidden;
}

.interactive-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    padding: 1rem 1.5rem 0.5rem;
}

.interactive-card p {
    padding: 0 1.5rem 1.5rem;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* Interactive Demo Animations */
.demo-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.nuclei-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.nucleus {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    transition: var(--transition);
}

.nucleus.active {
    background: radial-gradient(circle, #10b981, #059669);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

.nucleus.decayed {
    background: radial-gradient(circle, #6b7280, #4b5563);
    opacity: 0.5;
}

@keyframes glow {
    from { box-shadow: 0 0 5px rgba(16, 185, 129, 0.3); }
    to { box-shadow: 0 0 15px rgba(16, 185, 129, 0.8); }
}

.demo-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.demo-btn:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.demo-atom {
    position: relative;
    width: 120px;
    height: 120px;
}

.demo-nucleus {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #f59e0b, #d97706);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.demo-orbit {
    position: absolute;
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.orbit-1 {
    width: 60px;
    height: 60px;
    animation: rotate 3s linear infinite;
}

.orbit-2 {
    width: 90px;
    height: 90px;
    animation: rotate 5s linear infinite reverse;
}

.demo-electron {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #3b82f6;
    border-radius: 50%;
    top: -3px;
    right: -3px;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.7);
}

.demo-detector {
    position: relative;
    width: 100px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.detector-crystal {
    width: 80px;
    height: 20px;
    background: linear-gradient(to bottom, #10b981, #059669);
    border-radius: 4px;
    margin-bottom: 5px;
    position: relative;
    overflow: hidden;
}

.detector-pmt {
    width: 60px;
    height: 30px;
    background: linear-gradient(to bottom, #6b7280, #4b5563);
    border-radius: 4px;
}

.radiation-pulse {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 10px;
    background: #f59e0b;
    animation: pulse-down 2s ease-in-out infinite;
}

@keyframes pulse-down {
    0% { top: -10px; opacity: 1; }
    50% { top: 20px; opacity: 0.5; }
    100% { top: 50px; opacity: 0; }
}

.demo-calculator {
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    width: 150px;
}

.calc-display {
    text-align: center;
    margin-bottom: 1rem;
}

.calc-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.calc-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.calc-slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
}

.calc-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.calc-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

/* About Section */
.about {
    padding: 80px 0;
    background-color: white;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.about-features {
    margin-bottom: 2rem;
}

.about-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.about-feature i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: 0.25rem;
}

.about-feature h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.about-feature p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.learning-objectives h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.learning-objectives ul {
    list-style: none;
}

.learning-objectives li {
    position: relative;
    padding-right: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
}

.learning-objectives li::before {
    content: "✓";
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.stats-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stat-item {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    color: white;
}

.contact .section-title,
.contact .section-subtitle {
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.6);
}

.footer-links-bottom {
    display: flex;
    gap: 1rem;
}

.footer-links-bottom a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-links-bottom a:hover {
    color: var(--primary-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: var(--primary-color);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    z-index: 3000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .highlight {
        font-size: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-md);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero {
        padding: 100px 0 60px;
        min-height: auto;
    }

    .hero-title {
        font-size: 2rem;
    }

    .highlight {
        font-size: 1.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .atom-animation {
        width: 200px;
        height: 200px;
    }

    .features-grid,
    .chapters-grid,
    .interactive-grid {
        grid-template-columns: 1fr;
    }

    .chapter-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chapter-meta {
        align-items: flex-start;
        flex-direction: row;
        gap: 1rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .highlight {
        font-size: 1.3rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .feature-card,
    .chapter-card,
    .interactive-card {
        margin: 0 10px;
    }

    .chapter-features {
        flex-direction: column;
        gap: 0.25rem;
    }

    .feature-tag {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .modal-content {
        width: 98%;
        margin: 5% auto;
    }

    .modal-body {
        padding: 1rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .nav-toggle,
    .hero-buttons,
    .interactive-demo,
    .contact,
    .footer {
        display: none !important;
    }

    .hero {
        background: none !important;
        color: black !important;
        padding: 2rem 0;
    }

    .section-title {
        color: black !important;
        page-break-after: avoid;
    }

    .chapter-card {
        page-break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }

    .feature-card {
        page-break-inside: avoid;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #000080;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --border-color: #374151;
        --light-color: #1f2937;
    }

    body {
        background-color: #111827;
        color: var(--text-primary);
    }

    .feature-card,
    .chapter-card,
    .interactive-card {
        background-color: #1f2937;
        border-color: var(--border-color);
    }

    .modal-content {
        background-color: #1f2937;
        color: var(--text-primary);
    }
}

/* Chapter Content Styles */
.chapter-section {
    margin-bottom: 3rem;
}

.chapter-section h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.chapter-section h3 {
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem;
}

.chapter-section p {
    line-height: 1.8;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.chapter-section ul {
    margin: 1rem 0;
    padding-right: 2rem;
}

.chapter-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.equation {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #bae6fd;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    font-family: 'Times New Roman', serif;
}

.chapter-figure {
    margin: 2rem 0;
    text-align: center;
}

.figure-placeholder {
    background: var(--light-color);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
}

.figure-placeholder i {
    font-size: 3rem;
    color: var(--primary-color);
}

.figure-caption {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.interactive-element {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border: 1px solid #bbf7d0;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin: 2rem 0;
}

.interactive-element h4 {
    color: var(--success-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.interactive-placeholder {
    background: white;
    border: 2px dashed var(--success-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    color: var(--success-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.interactive-placeholder:hover {
    background: rgba(16, 185, 129, 0.05);
    transform: translateY(-2px);
}

.interactive-placeholder i {
    font-size: 2rem;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 2rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-light { background-color: var(--light-color); }
.bg-white { background-color: white; }

.border { border: 1px solid var(--border-color); }
.border-primary { border-color: var(--primary-color); }
.border-success { border-color: var(--success-color); }

.rounded { border-radius: var(--border-radius); }
.rounded-full { border-radius: 50%; }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.transition { transition: var(--transition); }

.cursor-pointer { cursor: pointer; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.grid { display: grid; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
