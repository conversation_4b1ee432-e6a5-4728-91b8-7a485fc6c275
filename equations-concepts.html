<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المعادلات والمفاهيم العلمية - منصة التصوير الطبي النووي</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/contrib/auto-render.min.js"></script>
    <style>
        .content-page {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: #1e40af;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .content-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .search-bar {
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .search-input {
            width: 100%;
            max-width: 500px;
            padding: 0.75rem 1rem;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }
        
        .equation-card, .concept-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .equation-card:hover, .concept-card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chapter-badge {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .category-badge {
            background: #10b981;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .card-content {
            padding: 1.5rem;
        }
        
        .equation-formula {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            margin: 1rem 0;
            border: 1px solid #e5e7eb;
        }
        
        .katex-equation {
            font-size: 1.2rem;
        }
        
        .equation-description, .concept-definition {
            color: #4b5563;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .variables-section, .related-section {
            margin-top: 1rem;
        }
        
        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .variables-list {
            list-style: none;
            padding: 0;
        }
        
        .variables-list li {
            padding: 0.25rem 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .variable-symbol {
            font-weight: 600;
            color: #1f2937;
        }
        
        .applications-tags, .related-terms {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .application-tag, .related-term {
            background: #dbeafe;
            color: #1e40af;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .related-term {
            background: #d1fae5;
            color: #065f46;
        }
        
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .back-button {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .content-page {
                padding: 1rem;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .content-tabs {
                flex-direction: column;
            }
            
            .filter-buttons {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </button>
    
    <div class="content-page">
        <div class="page-header">
            <h1 class="page-title">المعادلات الرياضية والمفاهيم العلمية</h1>
            <p class="page-subtitle">مرجع شامل للمعادلات والمفاهيم في فيزياء وتكنولوجيا التصوير الطبي النووي</p>
            
            <div class="stats-summary">
                <div class="stat-item">
                    <span class="stat-number">40+</span>
                    <span class="stat-label">معادلة رياضية</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100+</span>
                    <span class="stat-label">مفهوم علمي</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">10</span>
                    <span class="stat-label">فصول شاملة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">15+</span>
                    <span class="stat-label">تصنيف موضوعي</span>
                </div>
            </div>
        </div>
        
        <div class="content-tabs">
            <button class="tab-button active" onclick="showTab('equations')">
                <i class="fas fa-calculator"></i> المعادلات الرياضية
            </button>
            <button class="tab-button" onclick="showTab('concepts')">
                <i class="fas fa-book"></i> المفاهيم العلمية
            </button>
        </div>
        
        <!-- Equations Tab -->
        <div id="equations-tab" class="tab-content active">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="البحث في المعادلات..." 
                       onkeyup="searchEquations(this.value)">
            </div>
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterEquations('all')">الكل</button>
                <button class="filter-btn" onclick="filterEquations(1)">الفصل 1</button>
                <button class="filter-btn" onclick="filterEquations(2)">الفصل 2</button>
                <button class="filter-btn" onclick="filterEquations(3)">الفصل 3</button>
                <button class="filter-btn" onclick="filterEquations(4)">الفصل 4</button>
                <button class="filter-btn" onclick="filterEquations(5)">الفصل 5</button>
            </div>
            
            <div id="equations-grid" class="content-grid">
                <!-- Equations will be populated by JavaScript -->
            </div>
        </div>
        
        <!-- Concepts Tab -->
        <div id="concepts-tab" class="tab-content">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="البحث في المفاهيم..." 
                       onkeyup="searchConcepts(this.value)">
            </div>
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterConcepts('all')">الكل</button>
                <button class="filter-btn" onclick="filterConcepts('فيزياء نووية')">فيزياء نووية</button>
                <button class="filter-btn" onclick="filterConcepts('كشف الإشعاع')">كشف الإشعاع</button>
                <button class="filter-btn" onclick="filterConcepts('تقنيات التصوير')">تقنيات التصوير</button>
                <button class="filter-btn" onclick="filterConcepts('وحدات القياس')">وحدات القياس</button>
            </div>
            
            <div id="concepts-grid" class="content-grid">
                <!-- Concepts will be populated by JavaScript -->
            </div>
        </div>
    </div>
    
    <script src="equations-concepts.js"></script>
    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        // Populate equations
        function populateEquations(equations = mathematicalEquations) {
            const grid = document.getElementById('equations-grid');
            grid.innerHTML = equations.map(eq => renderEquation(eq)).join('');
            
            // Render KaTeX equations
            renderMathInElement(grid, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ]
            });
        }
        
        // Populate concepts
        function populateConcepts(concepts = scientificConcepts) {
            const grid = document.getElementById('concepts-grid');
            grid.innerHTML = concepts.map(concept => renderConcept(concept)).join('');
        }
        
        // Search functions
        function searchEquations(query) {
            const filtered = mathematicalEquations.filter(eq => 
                eq.title.includes(query) || 
                eq.description.includes(query) ||
                Object.values(eq.variables).some(v => v.includes(query))
            );
            populateEquations(filtered);
        }
        
        function searchConcepts(query) {
            const filtered = scientificConcepts.filter(concept => 
                concept.term.includes(query) || 
                concept.definition.includes(query) ||
                concept.category.includes(query)
            );
            populateConcepts(filtered);
        }
        
        // Filter functions
        function filterEquations(chapter) {
            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            if (chapter === 'all') {
                populateEquations();
            } else {
                const filtered = mathematicalEquations.filter(eq => eq.chapter === chapter);
                populateEquations(filtered);
            }
        }
        
        function filterConcepts(category) {
            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            if (category === 'all') {
                populateConcepts();
            } else {
                const filtered = scientificConcepts.filter(concept => concept.category === category);
                populateConcepts(filtered);
            }
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            populateEquations();
            populateConcepts();
        });
    </script>
</body>
</html>
