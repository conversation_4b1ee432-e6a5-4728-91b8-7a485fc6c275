<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرسوم والمخططات المتقدمة - منصة التصوير الطبي النووي</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .advanced-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
            padding: 2rem;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .visualization-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .visualization-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .visualization-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .card-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin: 0;
        }

        .visualization-container {
            min-height: 300px;
            background: #f8fafc;
            border-radius: 12px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .control-panel {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .control-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .control-btn.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .stats-panel {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .back-button {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1rem;
            border-radius: 12px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .advanced-page {
                padding: 1rem;
            }
            
            .visualization-grid {
                grid-template-columns: 1fr;
            }
            
            .page-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <button class="back-button" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </button>

    <div class="advanced-page">
        <div class="page-header">
            <h1 class="page-title">الرسوم والمخططات المتقدمة</h1>
            <p class="page-subtitle">
                استكشف مجموعة شاملة من الرسوم التفاعلية والمحاكيات المتقدمة لفهم فيزياء وتكنولوجيا التصوير الطبي النووي بشكل عملي وتفاعلي
            </p>
        </div>

        <div class="visualization-grid">
            <!-- بطاقة الذرة المتحركة -->
            <div class="visualization-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-atom"></i>
                    </div>
                    <div>
                        <h3 class="card-title">نموذج الذرة التفاعلي</h3>
                        <p class="card-description">محاكاة ثلاثية الأبعاد لبنية الذرة والإلكترونات</p>
                    </div>
                </div>
                <div class="visualization-container" id="atom-container"></div>
                <div class="control-panel">
                    <button class="control-btn" onclick="resetAtomAnimation()">
                        <i class="fas fa-redo"></i> إعادة تشغيل
                    </button>
                    <button class="control-btn secondary" onclick="pauseAtomAnimation()">
                        <i class="fas fa-pause"></i> إيقاف مؤقت
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value">3</span>
                        <span class="stat-label">إلكترونات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">1</span>
                        <span class="stat-label">نواة</span>
                    </div>
                </div>
            </div>

            <!-- بطاقة الاضمحلال الإشعاعي -->
            <div class="visualization-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-radiation"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي الاضمحلال الإشعاعي</h3>
                        <p class="card-description">مراقبة عملية الاضمحلال الإشعاعي في الوقت الفعلي</p>
                    </div>
                </div>
                <div class="visualization-container" id="decay-container"></div>
                <div class="control-panel">
                    <button class="control-btn" onclick="startDecaySimulation()">
                        <i class="fas fa-play"></i> بدء المحاكاة
                    </button>
                    <button class="control-btn secondary" onclick="resetDecaySimulation()">
                        <i class="fas fa-stop"></i> إيقاف
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="active-nuclei">1000</span>
                        <span class="stat-label">نوى نشطة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="decayed-nuclei">0</span>
                        <span class="stat-label">نوى متحللة</span>
                    </div>
                </div>
            </div>

            <!-- بطاقة كاميرا جاما -->
            <div class="visualization-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي كاميرا جاما</h3>
                        <p class="card-description">كيفية عمل كاميرا جاما في كشف الإشعاع</p>
                    </div>
                </div>
                <div class="visualization-container" id="gamma-camera-container"></div>
                <div class="control-panel">
                    <button class="control-btn" onclick="startGammaCameraSimulation()">
                        <i class="fas fa-power-off"></i> تشغيل الكاميرا
                    </button>
                    <button class="control-btn secondary" onclick="adjustSensitivity()">
                        <i class="fas fa-sliders-h"></i> ضبط الحساسية
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="detected-photons">0</span>
                        <span class="stat-label">فوتونات مكتشفة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="detection-rate">0</span>
                        <span class="stat-label">معدل الكشف</span>
                    </div>
                </div>
            </div>

            <!-- بطاقة PET -->
            <div class="visualization-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-circle-notch"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي PET</h3>
                        <p class="card-description">عملية الفناء وكشف التزامن في PET</p>
                    </div>
                </div>
                <div class="visualization-container" id="pet-container"></div>
                <div class="control-panel">
                    <button class="control-btn" onclick="startPETSimulation()">
                        <i class="fas fa-play-circle"></i> بدء المسح
                    </button>
                    <button class="control-btn secondary" onclick="adjustPETSettings()">
                        <i class="fas fa-cog"></i> الإعدادات
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="annihilation-events">0</span>
                        <span class="stat-label">أحداث الفناء</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="coincidence-lines">0</span>
                        <span class="stat-label">خطوط التزامن</span>
                    </div>
                </div>
            </div>

            <!-- بطاقة طيف الطاقة -->
            <div class="visualization-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محلل طيف الطاقة</h3>
                        <p class="card-description">تحليل تفاعلي لطيف طاقة أشعة جاما</p>
                    </div>
                </div>
                <div class="visualization-container" id="spectrum-container"></div>
                <div class="control-panel">
                    <button class="control-btn" onclick="changeIsotope('Tc99m')">
                        <i class="fas fa-atom"></i> Tc-99m
                    </button>
                    <button class="control-btn" onclick="changeIsotope('I123')">
                        <i class="fas fa-atom"></i> I-123
                    </button>
                    <button class="control-btn secondary" onclick="calibrateSpectrum()">
                        <i class="fas fa-balance-scale"></i> معايرة
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="peak-energy">140</span>
                        <span class="stat-label">طاقة القمة (keV)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="resolution">10</span>
                        <span class="stat-label">الدقة (%)</span>
                    </div>
                </div>
            </div>

            <!-- بطاقة التحكم في الجودة -->
            <div class="visualization-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h3 class="card-title">مراقبة الجودة</h3>
                        <p class="card-description">مراقبة معايير الأداء والجودة</p>
                    </div>
                </div>
                <div class="visualization-container" id="quality-control-container"></div>
                <div class="control-panel">
                    <button class="control-btn" onclick="runQualityTest()">
                        <i class="fas fa-play"></i> تشغيل الاختبار
                    </button>
                    <button class="control-btn secondary" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i> تقرير
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="uniformity">95</span>
                        <span class="stat-label">التجانس (%)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="resolution-qc">8.5</span>
                        <span class="stat-label">الدقة (mm)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="advanced-animations.js"></script>
    <script src="interactive-icons.js"></script>
    <script src="figures-diagrams.js"></script>

    <script>
        // إنشاء الجسيمات العائمة
        function createFloatingParticles() {
            const particlesContainer = document.getElementById('particles');
            
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تهيئة الرسوم المتحركة
        let atomAnimation, decayAnimation, gammaCameraAnimation, petAnimation, spectrumVisualization;

        function initializeVisualizations() {
            // إنشاء رسم الذرة
            atomAnimation = createAtomVisualization('atom-container', {
                width: 350,
                height: 300
            });

            // إنشاء محاكي الاضمحلال
            decayAnimation = createDecayVisualization('decay-container', {
                width: 350,
                height: 300,
                initialCount: 1000,
                halfLife: 100
            });

            // إنشاء محاكي كاميرا جاما
            gammaCameraAnimation = createGammaCameraVisualization('gamma-camera-container', {
                width: 350,
                height: 300
            });

            // إنشاء محاكي PET
            petAnimation = createPETVisualization('pet-container', {
                width: 350,
                height: 350
            });

            // إنشاء محلل الطيف
            spectrumVisualization = createSpectrumVisualization('spectrum-container', {
                width: 350,
                height: 300,
                peakEnergy: 140,
                resolution: 0.1
            });

            // إنشاء مخطط مراقبة الجودة
            createQualityControlChart();
        }

        // دوال التحكم
        function resetAtomAnimation() {
            document.getElementById('atom-container').innerHTML = '';
            atomAnimation = createAtomVisualization('atom-container', {
                width: 350,
                height: 300
            });
        }

        function pauseAtomAnimation() {
            // تنفيذ إيقاف مؤقت
            console.log('إيقاف مؤقت للذرة');
        }

        function startDecaySimulation() {
            document.getElementById('decay-container').innerHTML = '';
            decayAnimation = createDecayVisualization('decay-container', {
                width: 350,
                height: 300,
                initialCount: 1000,
                halfLife: 100
            });
        }

        function resetDecaySimulation() {
            document.getElementById('decay-container').innerHTML = '';
        }

        function startGammaCameraSimulation() {
            document.getElementById('gamma-camera-container').innerHTML = '';
            gammaCameraAnimation = createGammaCameraVisualization('gamma-camera-container', {
                width: 350,
                height: 300
            });
        }

        function adjustSensitivity() {
            alert('ضبط حساسية الكاميرا');
        }

        function startPETSimulation() {
            document.getElementById('pet-container').innerHTML = '';
            petAnimation = createPETVisualization('pet-container', {
                width: 350,
                height: 350
            });
        }

        function adjustPETSettings() {
            alert('إعدادات PET');
        }

        function changeIsotope(isotope) {
            let peakEnergy = 140;
            if (isotope === 'I123') peakEnergy = 159;
            
            document.getElementById('spectrum-container').innerHTML = '';
            spectrumVisualization = createSpectrumVisualization('spectrum-container', {
                width: 350,
                height: 300,
                peakEnergy: peakEnergy,
                resolution: 0.1
            });
            
            document.getElementById('peak-energy').textContent = peakEnergy;
        }

        function calibrateSpectrum() {
            alert('معايرة الطيف');
        }

        function runQualityTest() {
            // محاكاة اختبار الجودة
            const uniformity = 90 + Math.random() * 10;
            const resolution = 8 + Math.random() * 2;
            
            document.getElementById('uniformity').textContent = uniformity.toFixed(1);
            document.getElementById('resolution-qc').textContent = resolution.toFixed(1);
        }

        function generateReport() {
            alert('تم إنشاء تقرير الجودة');
        }

        function createQualityControlChart() {
            const container = document.getElementById('quality-control-container');
            const canvas = document.createElement('canvas');
            canvas.width = 350;
            canvas.height = 300;
            container.appendChild(canvas);

            const ctx = canvas.getContext('2d');
            
            // رسم مخطط بسيط لمراقبة الجودة
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let i = 0; i < 30; i++) {
                const x = (i / 29) * (canvas.width - 40) + 20;
                const y = canvas.height/2 + Math.sin(i * 0.2) * 50 + Math.random() * 20 - 10;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            // إضافة تسميات
            ctx.fillStyle = '#1f2937';
            ctx.font = '12px Cairo';
            ctx.fillText('مراقبة الأداء مع الزمن', 20, 20);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingParticles();
            initializeVisualizations();
        });
    </script>
</body>
</html>