<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المحاكيات المتقدمة - منصة التصوير الطبي النووي</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .simulators-page {
            background: linear-gradient(135deg, #2d1b69 0%, #11998e 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
            padding: 2rem;
        }

        .page-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.8;
        }

        .simulators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 3rem;
            max-width: 1600px;
            margin: 0 auto;
        }

        .simulator-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 2.5rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .simulator-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .simulator-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradient-shift 3s ease infinite;
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .card-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .card-title {
            font-size: 1.6rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 0.5rem 0;
        }

        .card-description {
            color: #6b7280;
            font-size: 1rem;
            margin: 0;
            line-height: 1.6;
        }

        .simulator-container {
            min-height: 400px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            border: 2px solid #e5e7eb;
        }

        .control-panel {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
            justify-content: center;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .control-btn:active {
            transform: translateY(-1px);
        }

        .control-btn.primary {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .control-btn.primary:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .control-btn.danger:hover {
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .control-btn.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        }

        .control-btn.secondary:hover {
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
        }

        .stats-panel {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 15px;
            padding: 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
        }

        .stat-value {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }

        .back-button {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 1rem 1.5rem;
            border-radius: 15px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            animation: float-up 8s infinite linear;
        }

        @keyframes float-up {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            z-index: 10;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .simulators-page {
                padding: 1rem;
            }
            
            .simulators-grid {
                grid-template-columns: 1fr;
            }
            
            .page-title {
                font-size: 2.5rem;
            }
            
            .simulator-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floating-elements"></div>
    
    <button class="back-button" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </button>

    <div class="simulators-page">
        <div class="page-header">
            <h1 class="page-title">المحاكيات المتقدمة</h1>
            <p class="page-subtitle">
                استكشف مجموعة شاملة من المحاكيات التفاعلية المتقدمة التي تحاكي العمليات الفيزيائية والتقنية في التصوير الطبي النووي بدقة عالية وواقعية مذهلة
            </p>
        </div>

        <div class="simulators-grid">
            <!-- محاكي SPECT -->
            <div class="simulator-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي SPECT المتقدم</h3>
                        <p class="card-description">محاكاة كاملة لعملية التصوير المقطعي بالإشعاع المفرد مع دوران الكاميرا وجمع الإسقاطات</p>
                    </div>
                </div>
                <div class="simulator-container" id="spect-container">
                    <div class="loading-overlay" id="spect-loading">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="control-btn primary" onclick="startSPECTScan()">
                        <i class="fas fa-play"></i> بدء المسح
                    </button>
                    <button class="control-btn secondary" onclick="pauseSPECTScan()">
                        <i class="fas fa-pause"></i> إيقاف مؤقت
                    </button>
                    <button class="control-btn danger" onclick="resetSPECTScan()">
                        <i class="fas fa-stop"></i> إعادة تعيين
                    </button>
                    <button class="control-btn" onclick="adjustSPECTSpeed()">
                        <i class="fas fa-tachometer-alt"></i> السرعة
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="spect-angle">0</span>
                        <span class="stat-label">الزاوية (درجة)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="spect-projections">0</span>
                        <span class="stat-label">الإسقاطات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="spect-progress">0</span>
                        <span class="stat-label">التقدم (%)</span>
                    </div>
                </div>
            </div>

            <!-- محاكي تفاعل الإشعاع -->
            <div class="simulator-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-radiation"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي تفاعل الإشعاع</h3>
                        <p class="card-description">مراقبة تفاعلات الفوتونات مع المادة بما في ذلك التأثير الكهروضوئي وتشتت كومبتون</p>
                    </div>
                </div>
                <div class="simulator-container" id="radiation-container">
                    <div class="loading-overlay" id="radiation-loading">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="control-btn primary" onclick="startRadiationSimulation()">
                        <i class="fas fa-power-off"></i> تشغيل المصدر
                    </button>
                    <button class="control-btn secondary" onclick="changeMaterial()">
                        <i class="fas fa-cube"></i> تغيير المادة
                    </button>
                    <button class="control-btn danger" onclick="resetRadiationSimulation()">
                        <i class="fas fa-trash"></i> مسح
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="radiation-photons">0</span>
                        <span class="stat-label">الفوتونات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="radiation-electrons">0</span>
                        <span class="stat-label">الإلكترونات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="radiation-interactions">0</span>
                        <span class="stat-label">التفاعلات</span>
                    </div>
                </div>
            </div>

            <!-- محاكي مولد التكنيشيوم -->
            <div class="simulator-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي مولد Tc-99m</h3>
                        <p class="card-description">محاكاة عمل مولد الموليبدينوم-تكنيشيوم مع عرض نمو النشاط والاستخراج</p>
                    </div>
                </div>
                <div class="simulator-container" id="generator-container">
                    <div class="loading-overlay" id="generator-loading">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="control-btn primary" onclick="eluteGenerator()">
                        <i class="fas fa-tint"></i> استخراج Tc-99m
                    </button>
                    <button class="control-btn secondary" onclick="checkGeneratorActivity()">
                        <i class="fas fa-search"></i> فحص النشاط
                    </button>
                    <button class="control-btn danger" onclick="resetGenerator()">
                        <i class="fas fa-redo"></i> مولد جديد
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="mo99-activity">1000</span>
                        <span class="stat-label">Mo-99 (GBq)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="tc99m-activity">0</span>
                        <span class="stat-label">Tc-99m (GBq)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="generator-time">0</span>
                        <span class="stat-label">الوقت (ساعة)</span>
                    </div>
                </div>
            </div>

            <!-- محاكي تروية القلب -->
            <div class="simulator-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div>
                        <h3 class="card-title">محاكي تروية القلب</h3>
                        <p class="card-description">محاكاة تروية عضلة القلب مع تخطيط القلب ومنحنيات التروية المختلفة</p>
                    </div>
                </div>
                <div class="simulator-container" id="cardiac-container">
                    <div class="loading-overlay" id="cardiac-loading">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="control-btn primary" onclick="startCardiacMonitoring()">
                        <i class="fas fa-play"></i> بدء المراقبة
                    </button>
                    <button class="control-btn secondary" onclick="adjustHeartRate()">
                        <i class="fas fa-heart"></i> معدل القلب
                    </button>
                    <button class="control-btn" onclick="simulateIschemia()">
                        <i class="fas fa-exclamation-triangle"></i> محاكاة نقص التروية
                    </button>
                    <button class="control-btn danger" onclick="resetCardiacSimulation()">
                        <i class="fas fa-stop"></i> إيقاف
                    </button>
                </div>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-value" id="heart-rate">70</span>
                        <span class="stat-label">معدل القلب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="perfusion-avg">85</span>
                        <span class="stat-label">متوسط التروية (%)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="cardiac-phase">انبساط</span>
                        <span class="stat-label">طور القلب</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="advanced-simulators.js"></script>
    <script src="advanced-animations.js"></script>
    <script src="interactive-icons.js"></script>

    <script>
        // إنشاء العناصر العائمة
        function createFloatingElements() {
            const container = document.getElementById('floating-elements');
            
            for (let i = 0; i < 30; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 8 + 's';
                element.style.animationDuration = (8 + Math.random() * 4) + 's';
                container.appendChild(element);
            }
        }

        // متغيرات المحاكيات
        let spectSimulator, radiationSimulator, generatorSimulator, cardiacSimulator;

        // تهيئة المحاكيات
        function initializeSimulators() {
            // إخفاء شاشات التحميل وتهيئة المحاكيات
            setTimeout(() => {
                document.getElementById('spect-loading').style.display = 'none';
                spectSimulator = createSPECTSimulator('spect-container', {
                    width: 450,
                    height: 400
                });
            }, 1000);

            setTimeout(() => {
                document.getElementById('radiation-loading').style.display = 'none';
                radiationSimulator = createRadiationInteractionSimulator('radiation-container', {
                    width: 450,
                    height: 400
                });
            }, 1500);

            setTimeout(() => {
                document.getElementById('generator-loading').style.display = 'none';
                generatorSimulator = createTechnetiumGeneratorSimulator('generator-container', {
                    width: 450,
                    height: 400
                });
            }, 2000);

            setTimeout(() => {
                document.getElementById('cardiac-loading').style.display = 'none';
                cardiacSimulator = createCardiacDopplerSimulator('cardiac-container', {
                    width: 450,
                    height: 400
                });
            }, 2500);
        }

        // دوال التحكم في SPECT
        function startSPECTScan() {
            if (spectSimulator) {
                spectSimulator.startScan();
            }
        }

        function pauseSPECTScan() {
            if (spectSimulator) {
                spectSimulator.stopScan();
            }
        }

        function resetSPECTScan() {
            if (spectSimulator) {
                spectSimulator.reset();
            }
        }

        function adjustSPECTSpeed() {
            const speed = prompt('أدخل سرعة المسح (1-10):', '2');
            if (speed && spectSimulator) {
                spectSimulator.setScanSpeed(parseFloat(speed));
            }
        }

        // دوال التحكم في محاكي الإشعاع
        function startRadiationSimulation() {
            // المحاكاة تعمل تلقائياً
            console.log('تم تشغيل مصدر الإشعاع');
        }

        function changeMaterial() {
            alert('تم تغيير المادة إلى الرصاص');
        }

        function resetRadiationSimulation() {
            if (radiationSimulator) {
                radiationSimulator.reset();
            }
        }

        // دوال التحكم في المولد
        function eluteGenerator() {
            if (generatorSimulator) {
                generatorSimulator.elute();
            }
        }

        function checkGeneratorActivity() {
            alert('فحص نشاط المولد');
        }

        function resetGenerator() {
            if (generatorSimulator) {
                generatorSimulator.reset();
            }
        }

        // دوال التحكم في القلب
        function startCardiacMonitoring() {
            console.log('بدء مراقبة القلب');
        }

        function adjustHeartRate() {
            const rate = prompt('أدخل معدل القلب (50-150):', '70');
            if (rate && cardiacSimulator) {
                cardiacSimulator.setHeartRate(parseInt(rate));
            }
        }

        function simulateIschemia() {
            if (cardiacSimulator) {
                cardiacSimulator.setPerfusion(0, 0.3); // تقليل تروية الجدار الأمامي
                alert('تم محاكاة نقص التروية في الجدار الأمامي');
            }
        }

        function resetCardiacSimulation() {
            if (cardiacSimulator) {
                cardiacSimulator.reset();
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            // تحديث إحصائيات SPECT
            // يمكن إضافة المزيد من التحديثات هنا

            setTimeout(updateStats, 1000);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingElements();
            initializeSimulators();
            updateStats();
        });
    </script>
</body>
</html>