<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأدوات التفاعلية - منصة التصوير الطبي النووي</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .tools-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 2rem;
            position: relative;
            overflow-x: hidden;
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }

        .bg-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .bg-shape:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-shape:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .bg-shape:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 4rem;
            padding: 2rem;
            position: relative;
        }

        .page-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f8ff, #e6f3ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: title-glow 3s ease-in-out infinite alternate;
        }

        @keyframes title-glow {
            from { filter: drop-shadow(0 0 10px rgba(255,255,255,0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(255,255,255,0.8)); }
        }

        .page-subtitle {
            font-size: 1.4rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.8;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 3rem;
            max-width: 1800px;
            margin: 0 auto;
        }

        .tool-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
            background-size: 400% 100%;
            animation: gradient-flow 4s ease infinite;
        }

        @keyframes gradient-flow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .tool-card:hover {
            transform: translateY(-20px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
        }

        .tool-header {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .tool-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            animation: icon-pulse 2s ease-in-out infinite alternate;
        }

        @keyframes icon-pulse {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }

        .tool-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 0.5rem 0;
        }

        .tool-description {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 0;
            line-height: 1.6;
        }

        .tool-container {
            min-height: 500px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 20px;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            border: 2px solid #e5e7eb;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .back-button {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 1rem 1.5rem;
            border-radius: 15px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 1rem;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            z-index: 10;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 6px solid #f3f4f6;
            border-top: 6px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .loading-text {
            color: #6b7280;
            font-size: 1.1rem;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .feature-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .tools-page {
                padding: 1rem;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .page-title {
                font-size: 2.5rem;
            }
            
            .tool-card {
                padding: 2rem;
            }
            
            .tool-header {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg">
        <div class="bg-shape"></div>
        <div class="bg-shape"></div>
        <div class="bg-shape"></div>
    </div>
    
    <button class="back-button" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </button>

    <div class="tools-page">
        <div class="page-header">
            <h1 class="page-title">الأدوات التفاعلية</h1>
            <p class="page-subtitle">
                مجموعة شاملة من الأدوات التفاعلية المتقدمة للحسابات والتحليلات في مجال التصوير الطبي النووي، 
                مصممة لتوفير تجربة تعليمية وعملية متميزة للطلاب والمختصين
            </p>
        </div>

        <div class="tools-grid">
            <!-- حاسبة الجرعة الإشعاعية -->
            <div class="tool-card">
                <div class="feature-badge">متقدم</div>
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div>
                        <h3 class="tool-title">حاسبة الجرعة الإشعاعية</h3>
                        <p class="tool-description">حساب دقيق للجرعة الإشعاعية بناءً على النشاط والوقت والمسافة مع تقييم مستوى الأمان</p>
                    </div>
                </div>
                <div class="tool-container" id="dose-calculator-container">
                    <div class="loading-overlay" id="dose-loading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">جاري تحميل الحاسبة...</div>
                    </div>
                </div>
            </div>

            <!-- محلل الطيف التفاعلي -->
            <div class="tool-card">
                <div class="feature-badge">تفاعلي</div>
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h3 class="tool-title">محلل الطيف التفاعلي</h3>
                        <p class="tool-description">تحليل وعرض أطياف الطاقة للنظائر المختلفة مع إمكانية التلاعب والتحليل المتقدم</p>
                    </div>
                </div>
                <div class="tool-container" id="spectrum-analyzer-container">
                    <div class="loading-overlay" id="spectrum-loading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">جاري تحميل المحلل...</div>
                    </div>
                </div>
            </div>

            <!-- محاكي الحماية الإشعاعية -->
            <div class="tool-card">
                <div class="feature-badge">محاكاة</div>
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h3 class="tool-title">محاكي الحماية الإشعاعية</h3>
                        <p class="tool-description">محاكاة تأثير مواد الحماية المختلفة على الإشعاع مع حساب معاملات التوهين</p>
                    </div>
                </div>
                <div class="tool-container" id="shielding-simulator-container">
                    <div class="loading-overlay" id="shielding-loading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">جاري تحميل المحاكي...</div>
                    </div>
                </div>
            </div>

            <!-- مخطط الاضمحلال التفاعلي -->
            <div class="tool-card">
                <div class="feature-badge">تعليمي</div>
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div>
                        <h3 class="tool-title">مخطط الاضمحلال التفاعلي</h3>
                        <p class="tool-description">عرض تفاعلي لسلاسل الاضمحلال الإشعاعي مع معلومات مفصلة عن كل نظير</p>
                    </div>
                </div>
                <div class="tool-container" id="decay-chain-container">
                    <div class="loading-overlay" id="decay-loading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">جاري تحميل المخطط...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="interactive-tools.js"></script>
    <script src="advanced-animations.js"></script>
    <script src="interactive-icons.js"></script>

    <script>
        // تهيئة الأدوات التفاعلية
        function initializeTools() {
            // تحميل حاسبة الجرعة
            setTimeout(() => {
                document.getElementById('dose-loading').style.display = 'none';
                createDoseCalculator('dose-calculator-container');
            }, 1000);

            // تحميل محلل الطيف
            setTimeout(() => {
                document.getElementById('spectrum-loading').style.display = 'none';
                createSpectrumAnalyzer('spectrum-analyzer-container');
            }, 1500);

            // تحميل محاكي الحماية
            setTimeout(() => {
                document.getElementById('shielding-loading').style.display = 'none';
                createShieldingSimulator('shielding-simulator-container');
            }, 2000);

            // تحميل مخطط الاضمحلال
            setTimeout(() => {
                document.getElementById('decay-loading').style.display = 'none';
                createDecayChainVisualization('decay-chain-container');
            }, 2500);
        }

        // إضافة تأثيرات بصرية إضافية
        function addVisualEffects() {
            // تأثير الجسيمات العائمة
            const particlesContainer = document.createElement('div');
            particlesContainer.style.position = 'fixed';
            particlesContainer.style.top = '0';
            particlesContainer.style.left = '0';
            particlesContainer.style.width = '100%';
            particlesContainer.style.height = '100%';
            particlesContainer.style.pointerEvents = 'none';
            particlesContainer.style.zIndex = '-1';
            
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.background = 'rgba(255, 255, 255, 0.3)';
                particle.style.borderRadius = '50%';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationName = 'particle-float';
                particle.style.animationDuration = (8 + Math.random() * 4) + 's';
                particle.style.animationIterationCount = 'infinite';
                particle.style.animationTimingFunction = 'linear';
                particle.style.animationDelay = Math.random() * 8 + 's';
                
                particlesContainer.appendChild(particle);
            }
            
            document.body.appendChild(particlesContainer);
            
            // إضافة CSS للرسوم المتحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes particle-float {
                    0% {
                        transform: translateY(100vh) rotate(0deg);
                        opacity: 0;
                    }
                    10% {
                        opacity: 1;
                    }
                    90% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-100px) rotate(360deg);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // تأثير التمرير السلس
        function addSmoothScrolling() {
            const toolCards = document.querySelectorAll('.tool-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });
            
            toolCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeTools();
            addVisualEffects();
            addSmoothScrolling();
        });

        // إضافة تأثيرات الماوس
        document.addEventListener('mousemove', function(e) {
            const shapes = document.querySelectorAll('.bg-shape');
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;
            
            shapes.forEach((shape, index) => {
                const speed = (index + 1) * 0.5;
                const xPos = x * speed * 50;
                const yPos = y * speed * 50;
                
                shape.style.transform = `translate(${xPos}px, ${yPos}px) rotate(${x * 360}deg)`;
            });
        });
    </script>
</body>
</html>