// Advanced Interactive Simulators
// المحاكيات التفاعلية المتقدمة

class AdvancedSimulators {
    constructor() {
        this.simulators = new Map();
        this.activeSimulations = new Set();
    }

    // محاكي SPECT متقدم
    createSPECTSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 600;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        // إعدادات المحاكاة
        let currentAngle = 0;
        let projections = [];
        let isScanning = false;
        let scanSpeed = options.scanSpeed || 2; // درجات في الإطار

        // المريض والأعضاء
        const patient = {
            x: centerX,
            y: centerY,
            organs: [
                { x: centerX - 30, y: centerY - 20, size: 25, activity: 0.9, name: 'القلب' },
                { x: centerX + 40, y: centerY + 10, size: 35, activity: 0.7, name: 'الكبد' },
                { x: centerX - 50, y: centerY + 30, size: 20, activity: 0.5, name: 'الكلى اليسرى' },
                { x: centerX + 20, y: centerY + 35, size: 20, activity: 0.5, name: 'الكلى اليمنى' }
            ]
        };

        // كاميرا جاما
        const camera = {
            distance: 200,
            width: 150,
            height: 20
        };

        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // رسم الخلفية
            ctx.fillStyle = '#1a1a2e';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // رسم المريض
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.beginPath();
            ctx.arc(patient.x, patient.y, 80, 0, Math.PI * 2);
            ctx.fill();

            // رسم الأعضاء
            patient.organs.forEach(organ => {
                ctx.beginPath();
                ctx.fillStyle = `rgba(255, 100, 100, ${organ.activity})`;
                ctx.arc(organ.x, organ.y, organ.size, 0, Math.PI * 2);
                ctx.fill();

                // إضافة تسمية
                ctx.fillStyle = 'white';
                ctx.font = '12px Cairo';
                ctx.textAlign = 'center';
                ctx.fillText(organ.name, organ.x, organ.y + organ.size + 15);
            });

            // حساب موقع الكاميرا
            const cameraX = centerX + Math.cos(currentAngle * Math.PI / 180) * camera.distance;
            const cameraY = centerY + Math.sin(currentAngle * Math.PI / 180) * camera.distance;

            // رسم الكاميرا
            ctx.save();
            ctx.translate(cameraX, cameraY);
            ctx.rotate(currentAngle * Math.PI / 180 + Math.PI / 2);
            
            ctx.fillStyle = '#333';
            ctx.fillRect(-camera.width / 2, -camera.height / 2, camera.width, camera.height);
            
            // رسم المجمع
            ctx.fillStyle = '#666';
            for (let i = 0; i < 10; i++) {
                const x = -camera.width / 2 + (i + 1) * (camera.width / 11);
                ctx.fillRect(x - 1, -camera.height / 2 - 10, 2, 10);
            }
            
            ctx.restore();

            // رسم خط الإسقاط
            if (isScanning) {
                ctx.strokeStyle = 'rgba(0, 255, 0, 0.5)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(cameraX, cameraY);
                
                const lineEndX = centerX - Math.cos(currentAngle * Math.PI / 180) * camera.distance;
                const lineEndY = centerY - Math.sin(currentAngle * Math.PI / 180) * camera.distance;
                ctx.lineTo(lineEndX, lineEndY);
                ctx.stroke();

                // جمع بيانات الإسقاط
                collectProjectionData();
            }

            // رسم الإسقاطات المجمعة
            drawProjections();

            // رسم معلومات المسح
            ctx.fillStyle = 'white';
            ctx.font = '16px Cairo';
            ctx.textAlign = 'right';
            ctx.fillText(`الزاوية: ${Math.round(currentAngle)}°`, canvas.width - 20, 30);
            ctx.fillText(`الإسقاطات: ${projections.length}`, canvas.width - 20, 50);
            ctx.fillText(`حالة المسح: ${isScanning ? 'جاري' : 'متوقف'}`, canvas.width - 20, 70);

            // تحديث الزاوية
            if (isScanning) {
                currentAngle += scanSpeed;
                if (currentAngle >= 360) {
                    currentAngle = 0;
                    isScanning = false;
                }
            }

            requestAnimationFrame(animate);
        };

        function collectProjectionData() {
            // محاكاة جمع بيانات الإسقاط
            const projectionData = [];
            for (let i = 0; i < 64; i++) {
                let intensity = 0;
                patient.organs.forEach(organ => {
                    const distance = Math.abs(Math.sin((currentAngle + i * 2) * Math.PI / 180));
                    intensity += organ.activity * Math.exp(-distance * 0.1);
                });
                projectionData.push(intensity + Math.random() * 0.1);
            }
            
            projections.push({
                angle: currentAngle,
                data: projectionData
            });
        }

        function drawProjections() {
            if (projections.length === 0) return;

            const projectionArea = {
                x: 20,
                y: canvas.height - 150,
                width: 200,
                height: 120
            };

            // رسم خلفية منطقة الإسقاطات
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(projectionArea.x, projectionArea.y, projectionArea.width, projectionArea.height);

            // رسم الإسقاطات
            projections.slice(-10).forEach((projection, index) => {
                const alpha = (index + 1) / 10;
                ctx.strokeStyle = `rgba(0, 255, 255, ${alpha})`;
                ctx.lineWidth = 1;
                ctx.beginPath();

                projection.data.forEach((intensity, i) => {
                    const x = projectionArea.x + (i / projection.data.length) * projectionArea.width;
                    const y = projectionArea.y + projectionArea.height - (intensity * projectionArea.height);
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                
                ctx.stroke();
            });

            // تسمية
            ctx.fillStyle = 'white';
            ctx.font = '12px Cairo';
            ctx.fillText('الإسقاطات', projectionArea.x, projectionArea.y - 5);
        }

        animate();

        return {
            canvas,
            startScan: () => { isScanning = true; },
            stopScan: () => { isScanning = false; },
            reset: () => { 
                projections = []; 
                currentAngle = 0; 
                isScanning = false; 
            },
            setScanSpeed: (speed) => { scanSpeed = speed; }
        };
    }

    // محاكي تفاعل الإشعاع مع المادة
    createRadiationInteractionSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 700;
        canvas.height = options.height || 500;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');

        // أنواع التفاعل
        const interactions = {
            photoelectric: { color: '#ff6b6b', name: 'التأثير الكهروضوئي' },
            compton: { color: '#4ecdc4', name: 'تشتت كومبتون' },
            pair: { color: '#45b7d1', name: 'إنتاج الأزواج' }
        };

        let photons = [];
        let electrons = [];
        let interactionEvents = [];

        // المادة (مكعبات تمثل الذرات)
        const material = [];
        for (let x = 200; x < 500; x += 30) {
            for (let y = 100; y < 400; y += 30) {
                material.push({
                    x: x,
                    y: y,
                    size: 20,
                    atomicNumber: 53, // يود
                    electrons: 53
                });
            }
        }

        const animate = () => {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // رسم المادة
            material.forEach(atom => {
                ctx.fillStyle = 'rgba(100, 100, 100, 0.3)';
                ctx.fillRect(atom.x - atom.size/2, atom.y - atom.size/2, atom.size, atom.size);
                
                ctx.strokeStyle = '#666';
                ctx.strokeRect(atom.x - atom.size/2, atom.y - atom.size/2, atom.size, atom.size);
            });

            // إنشاء فوتونات جديدة
            if (Math.random() < 0.02) {
                photons.push({
                    x: 50,
                    y: 100 + Math.random() * 300,
                    vx: 3 + Math.random() * 2,
                    vy: (Math.random() - 0.5) * 2,
                    energy: 100 + Math.random() * 400, // keV
                    life: 300
                });
            }

            // تحديث الفوتونات
            photons = photons.filter(photon => {
                photon.x += photon.vx;
                photon.y += photon.vy;
                photon.life--;

                // رسم الفوتون
                ctx.beginPath();
                ctx.fillStyle = '#ffff00';
                ctx.arc(photon.x, photon.y, 3, 0, Math.PI * 2);
                ctx.fill();

                // فحص التفاعل مع المادة
                const nearbyAtom = material.find(atom => 
                    Math.abs(photon.x - atom.x) < atom.size/2 && 
                    Math.abs(photon.y - atom.y) < atom.size/2
                );

                if (nearbyAtom) {
                    // تحديد نوع التفاعل بناءً على الطاقة
                    let interactionType;
                    if (photon.energy < 100) {
                        interactionType = 'photoelectric';
                    } else if (photon.energy < 1022) {
                        interactionType = 'compton';
                    } else {
                        interactionType = 'pair';
                    }

                    // إنشاء حدث تفاعل
                    interactionEvents.push({
                        x: photon.x,
                        y: photon.y,
                        type: interactionType,
                        energy: photon.energy,
                        time: Date.now()
                    });

                    // إنشاء إلكترون
                    electrons.push({
                        x: photon.x,
                        y: photon.y,
                        vx: (Math.random() - 0.5) * 4,
                        vy: (Math.random() - 0.5) * 4,
                        energy: photon.energy * 0.7,
                        life: 50
                    });

                    return false; // إزالة الفوتون
                }

                return photon.life > 0 && photon.x < canvas.width;
            });

            // تحديث الإلكترونات
            electrons = electrons.filter(electron => {
                electron.x += electron.vx;
                electron.y += electron.vy;
                electron.vx *= 0.98; // تباطؤ
                electron.vy *= 0.98;
                electron.life--;

                // رسم الإلكترون
                ctx.beginPath();
                ctx.fillStyle = '#00ff00';
                ctx.arc(electron.x, electron.y, 2, 0, Math.PI * 2);
                ctx.fill();

                return electron.life > 0;
            });

            // رسم أحداث التفاعل
            interactionEvents = interactionEvents.filter(event => {
                const age = Date.now() - event.time;
                if (age < 1000) {
                    const alpha = 1 - age / 1000;
                    const interaction = interactions[event.type];
                    
                    ctx.beginPath();
                    ctx.fillStyle = interaction.color + Math.floor(alpha * 255).toString(16).padStart(2, '0');
                    ctx.arc(event.x, event.y, 10 + age / 100, 0, Math.PI * 2);
                    ctx.fill();

                    return true;
                }
                return false;
            });

            // رسم الإحصائيات
            ctx.fillStyle = 'white';
            ctx.font = '14px Cairo';
            ctx.fillText(`الفوتونات: ${photons.length}`, 20, 30);
            ctx.fillText(`الإلكترونات: ${electrons.length}`, 20, 50);
            ctx.fillText(`التفاعلات: ${interactionEvents.length}`, 20, 70);

            // رسم مفتاح الألوان
            let legendY = 100;
            Object.entries(interactions).forEach(([type, info]) => {
                ctx.fillStyle = info.color;
                ctx.fillRect(20, legendY, 15, 15);
                ctx.fillStyle = 'white';
                ctx.fillText(info.name, 45, legendY + 12);
                legendY += 25;
            });

            requestAnimationFrame(animate);
        };

        animate();

        return {
            canvas,
            reset: () => {
                photons = [];
                electrons = [];
                interactionEvents = [];
            }
        };
    }

    // محاكي مولد التكنيشيوم
    createTechnetiumGeneratorSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 400;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');

        // حالة المولد
        let mo99Activity = 1000; // GBq
        let tc99mActivity = 0;
        let elutionVolume = 0;
        let lastElution = 0;
        let time = 0;

        // ثوابت الاضمحلال
        const lambda_mo99 = Math.log(2) / (66 * 3600); // per second
        const lambda_tc99m = Math.log(2) / (6 * 3600); // per second

        // مكونات المولد
        const generator = {
            column: { x: 250, y: 150, width: 100, height: 200 },
            saline: { x: 150, y: 100, width: 60, height: 80 },
            vial: { x: 350, y: 250, width: 60, height: 80 }
        };

        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // تحديث الأنشطة
            time += 0.1;
            const dt = 0.1;
            
            // اضمحلال Mo-99
            mo99Activity *= Math.exp(-lambda_mo99 * dt);
            
            // نمو واضمحلال Tc-99m
            const production = lambda_mo99 * mo99Activity;
            const decay = lambda_tc99m * tc99mActivity;
            tc99mActivity += (production - decay) * dt;

            // رسم خلفية المولد
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // رسم عمود الألومينا
            ctx.fillStyle = '#d4d4aa';
            ctx.fillRect(generator.column.x, generator.column.y, 
                        generator.column.width, generator.column.height);
            
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(generator.column.x, generator.column.y, 
                          generator.column.width, generator.column.height);

            // رسم Mo-99 في العمود
            const mo99Height = (mo99Activity / 1000) * generator.column.height;
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(generator.column.x, 
                        generator.column.y + generator.column.height - mo99Height,
                        generator.column.width, mo99Height);

            // رسم Tc-99m في العمود
            const tc99mHeight = (tc99mActivity / 500) * generator.column.height * 0.3;
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(generator.column.x + 10, 
                        generator.column.y + generator.column.height - tc99mHeight - 10,
                        generator.column.width - 20, tc99mHeight);

            // رسم المحلول الملحي
            ctx.fillStyle = '#87ceeb';
            ctx.fillRect(generator.saline.x, generator.saline.y, 
                        generator.saline.width, generator.saline.height);
            
            ctx.strokeStyle = '#333';
            ctx.strokeRect(generator.saline.x, generator.saline.y, 
                          generator.saline.width, generator.saline.height);

            // رسم القارورة
            ctx.fillStyle = '#f5f5f5';
            ctx.fillRect(generator.vial.x, generator.vial.y, 
                        generator.vial.width, generator.vial.height);
            
            ctx.strokeStyle = '#333';
            ctx.strokeRect(generator.vial.x, generator.vial.y, 
                          generator.vial.width, generator.vial.height);

            // رسم Tc-99m المستخرج
            if (elutionVolume > 0) {
                const elutedHeight = (elutionVolume / 10) * generator.vial.height;
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(generator.vial.x, 
                            generator.vial.y + generator.vial.height - elutedHeight,
                            generator.vial.width, elutedHeight);
            }

            // رسم الأنابيب
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 3;
            
            // أنبوب المحلول الملحي
            ctx.beginPath();
            ctx.moveTo(generator.saline.x + generator.saline.width, 
                      generator.saline.y + generator.saline.height/2);
            ctx.lineTo(generator.column.x, generator.column.y + 20);
            ctx.stroke();

            // أنبوب الاستخراج
            ctx.beginPath();
            ctx.moveTo(generator.column.x + generator.column.width, 
                      generator.column.y + generator.column.height - 20);
            ctx.lineTo(generator.vial.x, generator.vial.y + 20);
            ctx.stroke();

            // رسم المعلومات
            ctx.fillStyle = '#333';
            ctx.font = '16px Cairo';
            ctx.fillText('مولد Mo-99/Tc-99m', 20, 30);
            
            ctx.font = '14px Cairo';
            ctx.fillText(`نشاط Mo-99: ${mo99Activity.toFixed(1)} GBq`, 20, 60);
            ctx.fillText(`نشاط Tc-99m: ${tc99mActivity.toFixed(1)} GBq`, 20, 80);
            ctx.fillText(`الوقت: ${(time/10).toFixed(1)} ساعة`, 20, 100);
            ctx.fillText(`آخر استخراج: ${lastElution.toFixed(1)} ساعة`, 20, 120);

            // رسم منحنى النمو
            drawGrowthCurve();

            requestAnimationFrame(animate);
        };

        function drawGrowthCurve() {
            const curveArea = {
                x: 450,
                y: 50,
                width: 140,
                height: 100
            };

            // خلفية المنحنى
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(curveArea.x, curveArea.y, curveArea.width, curveArea.height);
            
            ctx.strokeStyle = '#333';
            ctx.strokeRect(curveArea.x, curveArea.y, curveArea.width, curveArea.height);

            // رسم منحنى Tc-99m
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < curveArea.width; i++) {
                const t = (i / curveArea.width) * 24; // 24 ساعة
                const activity = 1000 * (lambda_mo99 / (lambda_tc99m - lambda_mo99)) * 
                               (Math.exp(-lambda_mo99 * t * 3600) - Math.exp(-lambda_tc99m * t * 3600));
                
                const x = curveArea.x + i;
                const y = curveArea.y + curveArea.height - (activity / 500) * curveArea.height;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();

            // تسمية
            ctx.fillStyle = '#333';
            ctx.font = '12px Cairo';
            ctx.fillText('نمو Tc-99m', curveArea.x + 5, curveArea.y + 15);
        }

        animate();

        return {
            canvas,
            elute: () => {
                elutionVolume = tc99mActivity * 0.8; // 80% efficiency
                tc99mActivity *= 0.2; // 20% remains
                lastElution = time / 10;
            },
            reset: () => {
                mo99Activity = 1000;
                tc99mActivity = 0;
                elutionVolume = 0;
                lastElution = 0;
                time = 0;
            }
        };
    }

    // محاكي دوبلر للقلب
    createCardiacDopplerSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 400;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');

        // معاملات القلب
        let heartRate = 70; // bpm
        let time = 0;
        let bloodFlow = [];
        let ecgTrace = [];

        // مناطق القلب
        const heartRegions = [
            { name: 'الجدار الأمامي', x: 200, y: 150, perfusion: 1.0, color: '#ff6b6b' },
            { name: 'الجدار الجانبي', x: 150, y: 180, perfusion: 0.8, color: '#4ecdc4' },
            { name: 'الجدار السفلي', x: 200, y: 210, perfusion: 0.9, color: '#45b7d1' },
            { name: 'الحاجز', x: 250, y: 180, perfusion: 0.85, color: '#96ceb4' }
        ];

        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            time += 0.1;
            const heartPhase = Math.sin(time * (heartRate / 60) * 2 * Math.PI);

            // رسم القلب
            ctx.save();
            ctx.translate(200, 180);
            ctx.scale(1 + heartPhase * 0.1, 1 + heartPhase * 0.1);

            // رسم حجرات القلب
            ctx.fillStyle = 'rgba(255, 200, 200, 0.3)';
            ctx.beginPath();
            ctx.arc(0, 0, 60, 0, Math.PI * 2);
            ctx.fill();

            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.restore();

            // رسم مناطق التروية
            heartRegions.forEach(region => {
                const perfusionLevel = region.perfusion * (0.8 + 0.2 * Math.abs(heartPhase));
                
                ctx.beginPath();
                ctx.fillStyle = region.color + Math.floor(perfusionLevel * 255).toString(16).padStart(2, '0');
                ctx.arc(region.x, region.y, 20, 0, Math.PI * 2);
                ctx.fill();

                // تسمية المنطقة
                ctx.fillStyle = '#333';
                ctx.font = '12px Cairo';
                ctx.textAlign = 'center';
                ctx.fillText(region.name, region.x, region.y + 35);
                ctx.fillText(`${(perfusionLevel * 100).toFixed(0)}%`, region.x, region.y + 50);
            });

            // رسم تخطيط القلب
            drawECG();

            // رسم منحنى التروية
            drawPerfusionCurve();

            // رسم المعلومات
            ctx.fillStyle = '#333';
            ctx.font = '16px Cairo';
            ctx.textAlign = 'right';
            ctx.fillText('محاكي تروية القلب', canvas.width - 20, 30);
            ctx.fillText(`معدل القلب: ${heartRate} نبضة/دقيقة`, canvas.width - 20, 50);
            ctx.fillText(`الطور: ${heartPhase > 0 ? 'انقباض' : 'انبساط'}`, canvas.width - 20, 70);

            requestAnimationFrame(animate);
        };

        function drawECG() {
            const ecgArea = {
                x: 350,
                y: 50,
                width: 200,
                height: 80
            };

            // خلفية تخطيط القلب
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(ecgArea.x, ecgArea.y, ecgArea.width, ecgArea.height);

            // شبكة
            ctx.strokeStyle = 'rgba(0, 255, 0, 0.3)';
            ctx.lineWidth = 1;
            for (let i = 0; i < ecgArea.width; i += 10) {
                ctx.beginPath();
                ctx.moveTo(ecgArea.x + i, ecgArea.y);
                ctx.lineTo(ecgArea.x + i, ecgArea.y + ecgArea.height);
                ctx.stroke();
            }

            // إضافة نقطة جديدة لتخطيط القلب
            const ecgValue = generateECGValue(time * (heartRate / 60));
            ecgTrace.push(ecgValue);
            
            if (ecgTrace.length > ecgArea.width) {
                ecgTrace.shift();
            }

            // رسم تخطيط القلب
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.beginPath();

            ecgTrace.forEach((value, index) => {
                const x = ecgArea.x + index;
                const y = ecgArea.y + ecgArea.height/2 - value * ecgArea.height/4;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });

            ctx.stroke();

            // تسمية
            ctx.fillStyle = '#00ff00';
            ctx.font = '12px Cairo';
            ctx.fillText('تخطيط القلب', ecgArea.x + 5, ecgArea.y + 15);
        }

        function generateECGValue(phase) {
            const normalizedPhase = (phase % 1) * 2 * Math.PI;
            
            // موجة P
            if (normalizedPhase < 0.5) {
                return 0.3 * Math.sin(normalizedPhase * 4);
            }
            // مجمع QRS
            else if (normalizedPhase < 1.5) {
                return Math.sin((normalizedPhase - 0.5) * 6) * 2;
            }
            // موجة T
            else if (normalizedPhase < 3) {
                return 0.5 * Math.sin((normalizedPhase - 1.5) * 4);
            }
            
            return 0;
        }

        function drawPerfusionCurve() {
            const curveArea = {
                x: 350,
                y: 150,
                width: 200,
                height: 100
            };

            // خلفية المنحنى
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(curveArea.x, curveArea.y, curveArea.width, curveArea.height);
            
            ctx.strokeStyle = '#333';
            ctx.strokeRect(curveArea.x, curveArea.y, curveArea.width, curveArea.height);

            // رسم منحنيات التروية لكل منطقة
            heartRegions.forEach((region, regionIndex) => {
                ctx.strokeStyle = region.color;
                ctx.lineWidth = 2;
                ctx.beginPath();

                for (let i = 0; i < curveArea.width; i++) {
                    const t = (i / curveArea.width) * 10; // 10 ثوان
                    const perfusion = region.perfusion * (0.8 + 0.2 * Math.sin(t * (heartRate / 60) * 2 * Math.PI));
                    
                    const x = curveArea.x + i;
                    const y = curveArea.y + curveArea.height - perfusion * curveArea.height;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
            });

            // تسمية
            ctx.fillStyle = '#333';
            ctx.font = '12px Cairo';
            ctx.fillText('منحنيات التروية', curveArea.x + 5, curveArea.y + 15);
        }

        animate();

        return {
            canvas,
            setHeartRate: (rate) => { heartRate = rate; },
            setPerfusion: (regionIndex, perfusion) => {
                if (heartRegions[regionIndex]) {
                    heartRegions[regionIndex].perfusion = perfusion;
                }
            },
            reset: () => {
                time = 0;
                ecgTrace = [];
                heartRate = 70;
            }
        };
    }
}

// إنشاء مثيل عام للاستخدام
const advancedSimulators = new AdvancedSimulators();

// دوال مساعدة للاستخدام السهل
function createSPECTSimulator(containerId, options = {}) {
    return advancedSimulators.createSPECTSimulator(containerId, options);
}

function createRadiationInteractionSimulator(containerId, options = {}) {
    return advancedSimulators.createRadiationInteractionSimulator(containerId, options);
}

function createTechnetiumGeneratorSimulator(containerId, options = {}) {
    return advancedSimulators.createTechnetiumGeneratorSimulator(containerId, options);
}

function createCardiacDopplerSimulator(containerId, options = {}) {
    return advancedSimulators.createCardiacDopplerSimulator(containerId, options);
}