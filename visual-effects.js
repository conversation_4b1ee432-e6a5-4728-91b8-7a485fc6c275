// Advanced Visual Effects and Animations
// التأثيرات البصرية المتقدمة والرسوم المتحركة

class VisualEffects {
    constructor() {
        this.effects = new Map();
        this.particles = [];
        this.animations = new Set();
    }

    // نظام الجسيمات المتقدم
    createParticleSystem(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 800;
        canvas.height = options.height || 600;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        let particles = [];
        let time = 0;

        const config = {
            particleCount: options.particleCount || 100,
            particleSize: options.particleSize || 2,
            particleSpeed: options.particleSpeed || 1,
            particleColor: options.particleColor || '#4ecdc4',
            connectionDistance: options.connectionDistance || 100,
            mouseInteraction: options.mouseInteraction !== false
        };

        let mouse = { x: 0, y: 0 };

        // إضافة تفاعل الماوس
        if (config.mouseInteraction) {
            canvas.addEventListener('mousemove', (e) => {
                const rect = canvas.getBoundingClientRect();
                mouse.x = e.clientX - rect.left;
                mouse.y = e.clientY - rect.top;
            });
        }

        // إنشاء الجسيمات
        function initParticles() {
            particles = [];
            for (let i = 0; i < config.particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * config.particleSpeed,
                    vy: (Math.random() - 0.5) * config.particleSpeed,
                    size: Math.random() * config.particleSize + 1,
                    opacity: Math.random() * 0.5 + 0.5,
                    hue: Math.random() * 60 + 180 // ألوان زرقاء-خضراء
                });
            }
        }

        // تحديث الجسيمات
        function updateParticles() {
            particles.forEach(particle => {
                // تحديث الموقع
                particle.x += particle.vx;
                particle.y += particle.vy;

                // ارتداد من الحواف
                if (particle.x <= 0 || particle.x >= canvas.width) {
                    particle.vx *= -1;
                }
                if (particle.y <= 0 || particle.y >= canvas.height) {
                    particle.y *= -1;
                }

                // تأثير الماوس
                if (config.mouseInteraction) {
                    const dx = mouse.x - particle.x;
                    const dy = mouse.y - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 100) {
                        const force = (100 - distance) / 100;
                        particle.vx += dx * force * 0.01;
                        particle.vy += dy * force * 0.01;
                    }
                }

                // تحديث الشفافية
                particle.opacity = 0.5 + Math.sin(time * 0.02 + particle.x * 0.01) * 0.3;
            });
        }

        // رسم الجسيمات
        function drawParticles() {
            particles.forEach(particle => {
                ctx.save();
                ctx.globalAlpha = particle.opacity;
                ctx.fillStyle = `hsl(${particle.hue}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                
                // تأثير التوهج
                ctx.shadowColor = `hsl(${particle.hue}, 70%, 60%)`;
                ctx.shadowBlur = particle.size * 2;
                ctx.fill();
                
                ctx.restore();
            });
        }

        // رسم الروابط
        function drawConnections() {
            ctx.strokeStyle = 'rgba(78, 205, 196, 0.2)';
            ctx.lineWidth = 1;

            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < config.connectionDistance) {
                        const opacity = 1 - distance / config.connectionDistance;
                        ctx.globalAlpha = opacity * 0.5;
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                    }
                }
            }
            ctx.globalAlpha = 1;
        }

        // الرسوم المتحركة الرئيسية
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, 'rgba(26, 26, 46, 0.95)');
            gradient.addColorStop(1, 'rgba(17, 153, 142, 0.95)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            time++;
            updateParticles();
            drawConnections();
            drawParticles();

            requestAnimationFrame(animate);
        }

        initParticles();
        animate();

        return {
            canvas,
            updateConfig: (newConfig) => {
                Object.assign(config, newConfig);
                initParticles();
            },
            reset: () => {
                initParticles();
                time = 0;
            }
        };
    }

    // تأثير الموجة التفاعلية
    createWaveEffect(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 400;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        let waves = [];
        let time = 0;

        // إضافة موجة جديدة عند النقر
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            waves.push({
                x: x,
                y: y,
                radius: 0,
                maxRadius: 200,
                opacity: 1,
                speed: 3,
                frequency: 0.1,
                amplitude: 20
            });
        });

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            time++;

            // تحديث ورسم الموجات
            waves = waves.filter(wave => {
                wave.radius += wave.speed;
                wave.opacity = 1 - (wave.radius / wave.maxRadius);

                if (wave.opacity > 0) {
                    // رسم الموجة الرئيسية
                    ctx.strokeStyle = `rgba(78, 205, 196, ${wave.opacity})`;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(wave.x, wave.y, wave.radius, 0, Math.PI * 2);
                    ctx.stroke();

                    // رسم الموجات الثانوية
                    for (let i = 1; i <= 3; i++) {
                        const secondaryRadius = wave.radius - i * 20;
                        if (secondaryRadius > 0) {
                            ctx.strokeStyle = `rgba(78, 205, 196, ${wave.opacity * 0.5})`;
                            ctx.lineWidth = 2 - i * 0.5;
                            ctx.beginPath();
                            ctx.arc(wave.x, wave.y, secondaryRadius, 0, Math.PI * 2);
                            ctx.stroke();
                        }
                    }

                    // تأثير الجسيمات
                    const particleCount = Math.floor(wave.radius / 10);
                    for (let i = 0; i < particleCount; i++) {
                        const angle = (i / particleCount) * Math.PI * 2;
                        const particleX = wave.x + Math.cos(angle) * wave.radius;
                        const particleY = wave.y + Math.sin(angle) * wave.radius;
                        
                        ctx.fillStyle = `rgba(255, 255, 255, ${wave.opacity * 0.8})`;
                        ctx.beginPath();
                        ctx.arc(particleX, particleY, 2, 0, Math.PI * 2);
                        ctx.fill();
                    }

                    return true;
                }
                return false;
            });

            // إضافة موجات تلقائية
            if (Math.random() < 0.01) {
                waves.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    radius: 0,
                    maxRadius: 150,
                    opacity: 1,
                    speed: 2,
                    frequency: 0.1,
                    amplitude: 15
                });
            }

            requestAnimationFrame(animate);
        }

        animate();

        return {
            canvas,
            addWave: (x, y) => {
                waves.push({
                    x: x,
                    y: y,
                    radius: 0,
                    maxRadius: 200,
                    opacity: 1,
                    speed: 3,
                    frequency: 0.1,
                    amplitude: 20
                });
            },
            clear: () => {
                waves = [];
            }
        };
    }

    // تأثير الهولوجرام
    createHologramEffect(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 400;
        canvas.height = options.height || 400;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        let time = 0;

        const hologram = {
            text: options.text || 'NUCLEAR IMAGING',
            size: options.size || 24,
            color: options.color || '#00ffff',
            glitchIntensity: options.glitchIntensity || 0.1
        };

        function drawScanLines() {
            ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            
            for (let y = 0; y < canvas.height; y += 4) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        function drawGlitchEffect() {
            if (Math.random() < hologram.glitchIntensity) {
                const glitchHeight = Math.random() * 50 + 10;
                const glitchY = Math.random() * (canvas.height - glitchHeight);
                
                // تأثير الانزلاق
                const imageData = ctx.getImageData(0, glitchY, canvas.width, glitchHeight);
                const offset = (Math.random() - 0.5) * 20;
                ctx.putImageData(imageData, offset, glitchY);
                
                // تأثير تغيير الألوان
                ctx.fillStyle = `rgba(255, 0, 0, 0.1)`;
                ctx.fillRect(0, glitchY, canvas.width, glitchHeight);
            }
        }

        function drawHologramText() {
            ctx.save();
            
            // تأثير التوهج
            ctx.shadowColor = hologram.color;
            ctx.shadowBlur = 20;
            
            ctx.font = `${hologram.size}px 'Courier New', monospace`;
            ctx.fillStyle = hologram.color;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // تأثير الاهتزاز
            const shakeX = (Math.random() - 0.5) * 2;
            const shakeY = (Math.random() - 0.5) * 2;
            
            // رسم النص مع تأثير الشفافية المتغيرة
            const opacity = 0.7 + Math.sin(time * 0.1) * 0.3;
            ctx.globalAlpha = opacity;
            
            ctx.fillText(hologram.text, centerX + shakeX, centerY + shakeY);
            
            // رسم حدود النص
            ctx.strokeStyle = hologram.color;
            ctx.lineWidth = 1;
            ctx.globalAlpha = 0.5;
            ctx.strokeText(hologram.text, centerX + shakeX, centerY + shakeY);
            
            ctx.restore();
        }

        function drawHologramFrame() {
            // إطار الهولوجرام
            ctx.strokeStyle = hologram.color;
            ctx.lineWidth = 2;
            ctx.globalAlpha = 0.6;
            
            const margin = 20;
            ctx.strokeRect(margin, margin, canvas.width - margin * 2, canvas.height - margin * 2);
            
            // زوايا الإطار
            const cornerSize = 20;
            const corners = [
                [margin, margin],
                [canvas.width - margin, margin],
                [margin, canvas.height - margin],
                [canvas.width - margin, canvas.height - margin]
            ];
            
            corners.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.moveTo(x - cornerSize, y);
                ctx.lineTo(x, y);
                ctx.lineTo(x, y - cornerSize);
                ctx.stroke();
            });
            
            ctx.globalAlpha = 1;
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية داكنة
            ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            time++;

            drawScanLines();
            drawHologramFrame();
            drawHologramText();
            drawGlitchEffect();

            requestAnimationFrame(animate);
        }

        animate();

        return {
            canvas,
            setText: (text) => {
                hologram.text = text;
            },
            setColor: (color) => {
                hologram.color = color;
            },
            setGlitchIntensity: (intensity) => {
                hologram.glitchIntensity = intensity;
            }
        };
    }

    // تأثير الطاقة النووية
    createNuclearEnergyEffect(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 500;
        canvas.height = options.height || 500;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        let time = 0;
        let energyParticles = [];
        let coreTemperature = 0;

        // إنشاء جسيمات الطاقة
        function createEnergyParticles() {
            for (let i = 0; i < 5; i++) {
                const angle = Math.random() * Math.PI * 2;
                const speed = Math.random() * 3 + 1;
                
                energyParticles.push({
                    x: canvas.width / 2,
                    y: canvas.height / 2,
                    vx: Math.cos(angle) * speed,
                    vy: Math.sin(angle) * speed,
                    size: Math.random() * 4 + 2,
                    life: 100,
                    maxLife: 100,
                    color: `hsl(${Math.random() * 60 + 30}, 100%, 60%)` // ألوان دافئة
                });
            }
        }

        function drawNuclearCore() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const coreRadius = 30 + Math.sin(time * 0.1) * 5;
            
            // النواة الرئيسية
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, coreRadius);
            gradient.addColorStop(0, '#ffff00');
            gradient.addColorStop(0.5, '#ff6600');
            gradient.addColorStop(1, '#ff0000');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, coreRadius, 0, Math.PI * 2);
            ctx.fill();
            
            // تأثير التوهج
            ctx.shadowColor = '#ffff00';
            ctx.shadowBlur = 30;
            ctx.fill();
            ctx.shadowBlur = 0;
            
            // حلقات الطاقة
            for (let i = 1; i <= 3; i++) {
                const ringRadius = coreRadius + i * 20;
                const opacity = 0.3 - i * 0.1;
                
                ctx.strokeStyle = `rgba(255, 255, 0, ${opacity})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(centerX, centerY, ringRadius, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function updateEnergyParticles() {
            energyParticles = energyParticles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                
                // تأثير الجاذبية نحو المركز
                const dx = canvas.width / 2 - particle.x;
                const dy = canvas.height / 2 - particle.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 100) {
                    particle.vx += dx * 0.001;
                    particle.vy += dy * 0.001;
                }
                
                // رسم الجسيم
                const opacity = particle.life / particle.maxLife;
                ctx.globalAlpha = opacity;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                
                // مسار الجسيم
                ctx.strokeStyle = particle.color;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(particle.x, particle.y);
                ctx.lineTo(particle.x - particle.vx * 5, particle.y - particle.vy * 5);
                ctx.stroke();
                
                ctx.globalAlpha = 1;
                
                return particle.life > 0;
            });
        }

        function drawRadiationWaves() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            for (let i = 0; i < 5; i++) {
                const radius = (time * 2 + i * 30) % 200;
                const opacity = 1 - radius / 200;
                
                ctx.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية فضائية
            const gradient = ctx.createRadialGradient(
                canvas.width / 2, canvas.height / 2, 0,
                canvas.width / 2, canvas.height / 2, canvas.width / 2
            );
            gradient.addColorStop(0, 'rgba(0, 0, 50, 0.9)');
            gradient.addColorStop(1, 'rgba(0, 0, 0, 1)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            time++;

            // إنشاء جسيمات جديدة
            if (Math.random() < 0.1) {
                createEnergyParticles();
            }

            drawRadiationWaves();
            drawNuclearCore();
            updateEnergyParticles();

            requestAnimationFrame(animate);
        }

        animate();

        return {
            canvas,
            reset: () => {
                energyParticles = [];
                time = 0;
            },
            explode: () => {
                // تأثير الانفجار
                for (let i = 0; i < 50; i++) {
                    createEnergyParticles();
                }
            }
        };
    }

    // تأثير المصفوفة الرقمية
    createDigitalMatrixEffect(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 400;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        
        const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
        const fontSize = 14;
        const columns = Math.floor(canvas.width / fontSize);
        const drops = Array(columns).fill(0);

        function animate() {
            // خلفية شبه شفافة لتأثير الذيل
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#0f0';
            ctx.font = `${fontSize}px monospace`;

            for (let i = 0; i < drops.length; i++) {
                const text = characters[Math.floor(Math.random() * characters.length)];
                const x = i * fontSize;
                const y = drops[i] * fontSize;

                ctx.fillText(text, x, y);

                if (y > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }

            requestAnimationFrame(animate);
        }

        animate();

        return {
            canvas,
            reset: () => {
                drops.fill(0);
            }
        };
    }
}

// إنشاء مثيل عام للاستخدام
const visualEffects = new VisualEffects();

// دوال مساعدة للاستخدام السهل
function createParticleSystem(containerId, options = {}) {
    return visualEffects.createParticleSystem(containerId, options);
}

function createWaveEffect(containerId, options = {}) {
    return visualEffects.createWaveEffect(containerId, options);
}

function createHologramEffect(containerId, options = {}) {
    return visualEffects.createHologramEffect(containerId, options);
}

function createNuclearEnergyEffect(containerId, options = {}) {
    return visualEffects.createNuclearEnergyEffect(containerId, options);
}

function createDigitalMatrixEffect(containerId, options = {}) {
    return visualEffects.createDigitalMatrixEffect(containerId, options);
}