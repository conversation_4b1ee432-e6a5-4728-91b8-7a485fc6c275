// 3D Visualizations and Advanced Graphics
// الرسوم ثلاثية الأبعاد والرسومات المتقدمة

class ThreeDVisualizations {
    constructor() {
        this.visualizations = new Map();
        this.activeAnimations = new Set();
    }

    // نموذج ذرة ثلاثي الأبعاد متقدم
    create3DAtomModel(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 500;
        canvas.height = options.height || 500;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        // معاملات الذرة
        let rotationX = 0;
        let rotationY = 0;
        let rotationZ = 0;
        let time = 0;
        let selectedElement = 'hydrogen';

        // عناصر مختلفة
        const elements = {
            hydrogen: { protons: 1, neutrons: 0, electrons: 1, color: '#ff6b6b' },
            helium: { protons: 2, neutrons: 2, electrons: 2, color: '#4ecdc4' },
            carbon: { protons: 6, neutrons: 6, electrons: 6, color: '#45b7d1' },
            oxygen: { protons: 8, neutrons: 8, electrons: 8, color: '#96ceb4' },
            uranium: { protons: 92, neutrons: 146, electrons: 92, color: '#feca57' }
        };

        // إنشاء أزرار التحكم
        const controls = document.createElement('div');
        controls.className = 'atom-controls';
        controls.innerHTML = `
            <div class="control-panel">
                <h4>اختر العنصر:</h4>
                <div class="element-buttons">
                    ${Object.keys(elements).map(element => 
                        `<button class="element-btn" data-element="${element}">${element}</button>`
                    ).join('')}
                </div>
                <div class="atom-info" id="atom-info">
                    <div class="info-item">
                        <span>البروتونات:</span>
                        <span id="proton-count">1</span>
                    </div>
                    <div class="info-item">
                        <span>النيوترونات:</span>
                        <span id="neutron-count">0</span>
                    </div>
                    <div class="info-item">
                        <span>الإلكترونات:</span>
                        <span id="electron-count">1</span>
                    </div>
                </div>
            </div>
        `;

        // إضافة الأنماط
        const style = document.createElement('style');
        style.textContent = `
            .atom-controls {
                margin-top: 1rem;
                text-align: center;
            }

            .control-panel h4 {
                margin-bottom: 1rem;
                color: #1f2937;
            }

            .element-buttons {
                display: flex;
                justify-content: center;
                gap: 0.5rem;
                margin-bottom: 1rem;
                flex-wrap: wrap;
            }

            .element-btn {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                text-transform: capitalize;
            }

            .element-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }

            .element-btn.active {
                background: linear-gradient(135deg, #10b981, #059669);
            }

            .atom-info {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
                background: #f8fafc;
                padding: 1rem;
                border-radius: 8px;
                margin-top: 1rem;
            }

            .info-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.25rem;
            }

            .info-item span:first-child {
                font-weight: 600;
                color: #6b7280;
                font-size: 0.9rem;
            }

            .info-item span:last-child {
                font-weight: 700;
                color: #1f2937;
                font-size: 1.2rem;
            }
        `;

        document.head.appendChild(style);
        container.appendChild(controls);

        // إضافة مستمعي الأحداث
        controls.addEventListener('click', (e) => {
            if (e.target.classList.contains('element-btn')) {
                // إزالة الفئة النشطة من جميع الأزرار
                controls.querySelectorAll('.element-btn').forEach(btn => 
                    btn.classList.remove('active')
                );
                
                // إضافة الفئة النشطة للزر المحدد
                e.target.classList.add('active');
                
                selectedElement = e.target.dataset.element;
                updateAtomInfo();
            }
        });

        // تفعيل الزر الأول
        controls.querySelector('.element-btn').classList.add('active');

        function updateAtomInfo() {
            const element = elements[selectedElement];
            document.getElementById('proton-count').textContent = element.protons;
            document.getElementById('neutron-count').textContent = element.neutrons;
            document.getElementById('electron-count').textContent = element.electrons;
        }

        function project3D(x, y, z, rotX, rotY, rotZ) {
            // تطبيق الدوران
            let cosX = Math.cos(rotX), sinX = Math.sin(rotX);
            let cosY = Math.cos(rotY), sinY = Math.sin(rotY);
            let cosZ = Math.cos(rotZ), sinZ = Math.sin(rotZ);

            // دوران حول المحور X
            let y1 = y * cosX - z * sinX;
            let z1 = y * sinX + z * cosX;

            // دوران حول المحور Y
            let x2 = x * cosY + z1 * sinY;
            let z2 = -x * sinY + z1 * cosY;

            // دوران حول المحور Z
            let x3 = x2 * cosZ - y1 * sinZ;
            let y3 = x2 * sinZ + y1 * cosZ;

            // إسقاط ثلاثي الأبعاد إلى ثنائي الأبعاد
            const distance = 300;
            const scale = distance / (distance + z2);
            
            return {
                x: centerX + x3 * scale,
                y: centerY + y3 * scale,
                scale: scale
            };
        }

        function drawNucleus() {
            const element = elements[selectedElement];
            const nucleusRadius = 15;

            // رسم البروتونات
            for (let i = 0; i < element.protons; i++) {
                const angle = (i / element.protons) * Math.PI * 2;
                const radius = nucleusRadius * 0.7;
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;
                const z = Math.sin(time * 0.02 + i) * 5;

                const projected = project3D(x, y, z, rotationX, rotationY, rotationZ);
                
                ctx.beginPath();
                ctx.fillStyle = '#ff4757';
                ctx.arc(projected.x, projected.y, 4 * projected.scale, 0, Math.PI * 2);
                ctx.fill();
                
                // تأثير التوهج
                ctx.shadowColor = '#ff4757';
                ctx.shadowBlur = 10 * projected.scale;
                ctx.fill();
                ctx.shadowBlur = 0;
            }

            // رسم النيوترونات
            for (let i = 0; i < element.neutrons; i++) {
                const angle = (i / element.neutrons) * Math.PI * 2 + Math.PI / element.neutrons;
                const radius = nucleusRadius * 0.5;
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;
                const z = Math.cos(time * 0.02 + i) * 5;

                const projected = project3D(x, y, z, rotationX, rotationY, rotationZ);
                
                ctx.beginPath();
                ctx.fillStyle = '#3742fa';
                ctx.arc(projected.x, projected.y, 4 * projected.scale, 0, Math.PI * 2);
                ctx.fill();
                
                // تأثير التوهج
                ctx.shadowColor = '#3742fa';
                ctx.shadowBlur = 10 * projected.scale;
                ctx.fill();
                ctx.shadowBlur = 0;
            }
        }

        function drawElectronOrbits() {
            const element = elements[selectedElement];
            const orbitRadii = [60, 100, 140, 180]; // مدارات مختلفة
            
            // تحديد توزيع الإلكترونات في المدارات
            const electronDistribution = distributeElectrons(element.electrons);

            electronDistribution.forEach((electronsInOrbit, orbitIndex) => {
                if (electronsInOrbit === 0) return;

                const radius = orbitRadii[orbitIndex];
                
                // رسم المدار
                ctx.strokeStyle = `rgba(102, 126, 234, 0.3)`;
                ctx.lineWidth = 1;
                ctx.beginPath();
                
                for (let angle = 0; angle <= Math.PI * 2; angle += 0.1) {
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius * 0.3; // مدار مسطح
                    const z = 0;
                    
                    const projected = project3D(x, y, z, rotationX, rotationY, rotationZ);
                    
                    if (angle === 0) {
                        ctx.moveTo(projected.x, projected.y);
                    } else {
                        ctx.lineTo(projected.x, projected.y);
                    }
                }
                ctx.stroke();

                // رسم الإلكترونات
                for (let i = 0; i < electronsInOrbit; i++) {
                    const angle = (i / electronsInOrbit) * Math.PI * 2 + time * 0.01 * (orbitIndex + 1);
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius * 0.3;
                    const z = Math.sin(time * 0.02 + i) * 10;

                    const projected = project3D(x, y, z, rotationX, rotationY, rotationZ);
                    
                    ctx.beginPath();
                    ctx.fillStyle = element.color;
                    ctx.arc(projected.x, projected.y, 3 * projected.scale, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // مسار الإلكترون
                    ctx.strokeStyle = `${element.color}40`;
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    for (let j = 0; j < 20; j++) {
                        const trailAngle = angle - j * 0.1;
                        const trailX = Math.cos(trailAngle) * radius;
                        const trailY = Math.sin(trailAngle) * radius * 0.3;
                        const trailProjected = project3D(trailX, trailY, z, rotationX, rotationY, rotationZ);
                        
                        if (j === 0) {
                            ctx.moveTo(trailProjected.x, trailProjected.y);
                        } else {
                            ctx.lineTo(trailProjected.x, trailProjected.y);
                        }
                    }
                    ctx.stroke();
                }
            });
        }

        function distributeElectrons(totalElectrons) {
            // توزيع الإلكترونات حسب قواعد الكيمياء: 2, 8, 18, 32
            const maxPerOrbit = [2, 8, 18, 32];
            const distribution = [0, 0, 0, 0];
            let remaining = totalElectrons;

            for (let i = 0; i < maxPerOrbit.length && remaining > 0; i++) {
                const electronsInThisOrbit = Math.min(remaining, maxPerOrbit[i]);
                distribution[i] = electronsInThisOrbit;
                remaining -= electronsInThisOrbit;
            }

            return distribution;
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية متدرجة
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 250);
            gradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
            gradient.addColorStop(1, 'rgba(0, 0, 0, 0.95)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // تحديث الدوران
            rotationX += 0.005;
            rotationY += 0.008;
            rotationZ += 0.003;
            time++;

            // رسم المكونات
            drawElectronOrbits();
            drawNucleus();

            // رسم معلومات العنصر
            ctx.fillStyle = 'white';
            ctx.font = '16px Cairo';
            ctx.textAlign = 'center';
            ctx.fillText(selectedElement.toUpperCase(), centerX, 30);

            requestAnimationFrame(animate);
        }

        // بدء الرسوم المتحركة
        animate();
        updateAtomInfo();

        return {
            canvas,
            setElement: (element) => {
                if (elements[element]) {
                    selectedElement = element;
                    updateAtomInfo();
                }
            },
            reset: () => {
                rotationX = rotationY = rotationZ = time = 0;
            }
        };
    }

    // محاكي PET ثلاثي الأبعاد
    create3DPETSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 400;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        let time = 0;
        let detectorRing = [];
        let annihilationEvents = [];
        let coincidenceLines = [];

        // إنشاء حلقة الكواشف
        const numDetectors = 32;
        const ringRadius = 150;
        
        for (let i = 0; i < numDetectors; i++) {
            const angle = (i / numDetectors) * Math.PI * 2;
            detectorRing.push({
                angle: angle,
                x: centerX + Math.cos(angle) * ringRadius,
                y: centerY + Math.sin(angle) * ringRadius,
                active: false,
                lastHit: 0
            });
        }

        // إنشاء أزرار التحكم
        const controls = document.createElement('div');
        controls.className = 'pet-controls';
        controls.innerHTML = `
            <div class="control-panel">
                <button class="control-btn" onclick="startPETScan()">
                    <i class="fas fa-play"></i> بدء المسح
                </button>
                <button class="control-btn" onclick="pausePETScan()">
                    <i class="fas fa-pause"></i> إيقاف مؤقت
                </button>
                <button class="control-btn" onclick="resetPETScan()">
                    <i class="fas fa-stop"></i> إعادة تعيين
                </button>
                <div class="pet-stats">
                    <div class="stat-item">
                        <span>أحداث الفناء:</span>
                        <span id="annihilation-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span>خطوط التزامن:</span>
                        <span id="coincidence-count">0</span>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(controls);

        let isScanning = false;

        window.startPETScan = () => { isScanning = true; };
        window.pausePETScan = () => { isScanning = false; };
        window.resetPETScan = () => {
            isScanning = false;
            annihilationEvents = [];
            coincidenceLines = [];
            time = 0;
        };

        function createAnnihilationEvent() {
            if (!isScanning) return;

            // موقع عشوائي داخل الحلقة
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 80;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            // إنشاء فوتونين في اتجاهين متعاكسين
            const photonAngle = Math.random() * Math.PI * 2;
            
            const photon1 = {
                x: x,
                y: y,
                vx: Math.cos(photonAngle) * 4,
                vy: Math.sin(photonAngle) * 4,
                energy: 511, // keV
                life: 100
            };

            const photon2 = {
                x: x,
                y: y,
                vx: Math.cos(photonAngle + Math.PI) * 4,
                vy: Math.sin(photonAngle + Math.PI) * 4,
                energy: 511, // keV
                life: 100
            };

            annihilationEvents.push({
                x: x,
                y: y,
                photons: [photon1, photon2],
                time: time,
                detected: false
            });
        }

        function updateDetectors() {
            detectorRing.forEach(detector => {
                detector.active = false;
                
                // فحص الفوتونات القريبة
                annihilationEvents.forEach(event => {
                    event.photons.forEach(photon => {
                        const distance = Math.sqrt(
                            Math.pow(photon.x - detector.x, 2) + 
                            Math.pow(photon.y - detector.y, 2)
                        );
                        
                        if (distance < 15 && photon.life > 0) {
                            detector.active = true;
                            detector.lastHit = time;
                            photon.life = 0; // الفوتون تم كشفه
                        }
                    });
                });
            });
        }

        function detectCoincidences() {
            const activeDetectors = detectorRing.filter(d => d.active);
            
            if (activeDetectors.length >= 2) {
                // إنشاء خط تزامن بين أول كاشفين نشطين
                const det1 = activeDetectors[0];
                const det2 = activeDetectors[1];
                
                coincidenceLines.push({
                    x1: det1.x,
                    y1: det1.y,
                    x2: det2.x,
                    y2: det2.y,
                    time: time,
                    life: 60
                });
            }
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية
            ctx.fillStyle = '#1a1a2e';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            time++;

            // إنشاء أحداث فناء جديدة
            if (Math.random() < 0.03) {
                createAnnihilationEvent();
            }

            // تحديث الفوتونات
            annihilationEvents = annihilationEvents.filter(event => {
                event.photons.forEach(photon => {
                    if (photon.life > 0) {
                        photon.x += photon.vx;
                        photon.y += photon.vy;
                        photon.life--;

                        // رسم الفوتون
                        ctx.beginPath();
                        ctx.fillStyle = '#ffff00';
                        ctx.arc(photon.x, photon.y, 3, 0, Math.PI * 2);
                        ctx.fill();

                        // مسار الفوتون
                        ctx.strokeStyle = 'rgba(255, 255, 0, 0.3)';
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(photon.x, photon.y);
                        ctx.lineTo(photon.x - photon.vx * 10, photon.y - photon.vy * 10);
                        ctx.stroke();
                    }
                });

                return event.photons.some(p => p.life > 0) || (time - event.time < 100);
            });

            // تحديث الكواشف
            updateDetectors();
            detectCoincidences();

            // رسم حلقة الكواشف
            detectorRing.forEach(detector => {
                ctx.beginPath();
                ctx.fillStyle = detector.active ? '#00ff00' : '#666';
                ctx.arc(detector.x, detector.y, 8, 0, Math.PI * 2);
                ctx.fill();

                if (detector.active) {
                    // تأثير التوهج
                    ctx.shadowColor = '#00ff00';
                    ctx.shadowBlur = 15;
                    ctx.fill();
                    ctx.shadowBlur = 0;
                }
            });

            // رسم خطوط التزامن
            coincidenceLines = coincidenceLines.filter(line => {
                line.life--;
                
                const alpha = line.life / 60;
                ctx.strokeStyle = `rgba(0, 255, 255, ${alpha})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(line.x1, line.y1);
                ctx.lineTo(line.x2, line.y2);
                ctx.stroke();

                return line.life > 0;
            });

            // رسم المريض (دائرة في المنتصف)
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 80, 0, Math.PI * 2);
            ctx.stroke();

            // تحديث الإحصائيات
            document.getElementById('annihilation-count').textContent = annihilationEvents.length;
            document.getElementById('coincidence-count').textContent = coincidenceLines.length;

            requestAnimationFrame(animate);
        }

        animate();

        return {
            canvas,
            start: () => { isScanning = true; },
            pause: () => { isScanning = false; },
            reset: () => {
                isScanning = false;
                annihilationEvents = [];
                coincidenceLines = [];
                time = 0;
            }
        };
    }

    // محاكي كاميرا جاما ثلاثي الأبعاد
    create3DGammaCameraSimulator(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const canvas = document.createElement('canvas');
        canvas.width = options.width || 600;
        canvas.height = options.height || 500;
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');

        let rotationY = 0;
        let time = 0;
        let photons = [];
        let detectedEvents = [];

        // مكونات الكاميرا
        const camera = {
            collimator: { x: 200, y: 200, width: 200, height: 20, holes: 20 },
            crystal: { x: 200, y: 230, width: 200, height: 30 },
            pmt: { x: 200, y: 270, width: 200, height: 40, tubes: 8 }
        };

        // إنشاء أزرار التحكم
        const controls = document.createElement('div');
        controls.className = 'gamma-camera-controls';
        controls.innerHTML = `
            <div class="control-panel">
                <div class="control-group">
                    <label>نوع المجمع:</label>
                    <select id="collimator-type">
                        <option value="parallel">متوازي</option>
                        <option value="converging">متقارب</option>
                        <option value="diverging">متباعد</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>طاقة الفوتون (keV):</label>
                    <input type="range" id="photon-energy" min="50" max="500" value="140">
                    <span id="energy-display">140 keV</span>
                </div>
                <button class="control-btn" onclick="emitPhotons()">
                    <i class="fas fa-radiation"></i> إطلاق فوتونات
                </button>
                <div class="camera-stats">
                    <div class="stat-item">
                        <span>الفوتونات المكتشفة:</span>
                        <span id="detected-photons">0</span>
                    </div>
                    <div class="stat-item">
                        <span>الكفاءة:</span>
                        <span id="detection-efficiency">0%</span>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(controls);

        // مستمعي الأحداث
        document.getElementById('photon-energy').addEventListener('input', (e) => {
            document.getElementById('energy-display').textContent = e.target.value + ' keV';
        });

        window.emitPhotons = function() {
            for (let i = 0; i < 10; i++) {
                photons.push({
                    x: 50 + Math.random() * 100,
                    y: 150 + Math.random() * 200,
                    vx: 2 + Math.random() * 2,
                    vy: (Math.random() - 0.5) * 2,
                    energy: parseInt(document.getElementById('photon-energy').value),
                    life: 200,
                    detected: false
                });
            }
        };

        function project3D(x, y, z, rotY) {
            const cosY = Math.cos(rotY);
            const sinY = Math.sin(rotY);
            
            const x3d = x * cosY - z * sinY;
            const z3d = x * sinY + z * cosY;
            
            const distance = 400;
            const scale = distance / (distance + z3d);
            
            return {
                x: canvas.width / 2 + x3d * scale,
                y: y,
                scale: scale
            };
        }

        function drawCollimator() {
            const collimatorType = document.getElementById('collimator-type').value;
            
            // رسم جسم المجمع
            ctx.fillStyle = '#666';
            ctx.fillRect(camera.collimator.x, camera.collimator.y, 
                        camera.collimator.width, camera.collimator.height);

            // رسم الثقوب
            ctx.fillStyle = '#000';
            const holeWidth = camera.collimator.width / camera.collimator.holes;
            
            for (let i = 0; i < camera.collimator.holes; i++) {
                let holeX = camera.collimator.x + i * holeWidth + holeWidth / 4;
                let holeWidthActual = holeWidth / 2;
                
                // تعديل شكل الثقوب حسب نوع المجمع
                if (collimatorType === 'converging') {
                    holeWidthActual *= (1 - i / camera.collimator.holes * 0.5);
                } else if (collimatorType === 'diverging') {
                    holeWidthActual *= (1 + i / camera.collimator.holes * 0.5);
                }
                
                ctx.fillRect(holeX, camera.collimator.y, holeWidthActual, camera.collimator.height);
            }
        }

        function drawCrystal() {
            // البلورة الوامضة
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(camera.crystal.x, camera.crystal.y, 
                        camera.crystal.width, camera.crystal.height);
            
            // تأثير الوميض
            if (detectedEvents.length > 0) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
                ctx.fillRect(camera.crystal.x, camera.crystal.y, 
                            camera.crystal.width, camera.crystal.height);
            }
        }

        function drawPMTs() {
            // أنابيب المضاعف الضوئي
            const tubeWidth = camera.pmt.width / camera.pmt.tubes;
            
            for (let i = 0; i < camera.pmt.tubes; i++) {
                const tubeX = camera.pmt.x + i * tubeWidth;
                
                ctx.fillStyle = '#333';
                ctx.fillRect(tubeX, camera.pmt.y, tubeWidth - 2, camera.pmt.height);
                
                // إشارة كهربائية
                if (detectedEvents.some(event => 
                    event.x >= tubeX && event.x <= tubeX + tubeWidth && 
                    time - event.time < 30
                )) {
                    ctx.fillStyle = '#ffff00';
                    ctx.fillRect(tubeX, camera.pmt.y, tubeWidth - 2, camera.pmt.height);
                }
            }
        }

        function checkPhotonDetection(photon) {
            // فحص ما إذا كان الفوتون يمر عبر المجمع
            const holeWidth = camera.collimator.width / camera.collimator.holes;
            const holeIndex = Math.floor((photon.x - camera.collimator.x) / holeWidth);
            
            if (holeIndex >= 0 && holeIndex < camera.collimator.holes) {
                const holeX = camera.collimator.x + holeIndex * holeWidth + holeWidth / 4;
                const holeWidthActual = holeWidth / 2;
                
                if (photon.x >= holeX && photon.x <= holeX + holeWidthActual &&
                    photon.y >= camera.collimator.y && 
                    photon.y <= camera.collimator.y + camera.collimator.height) {
                    
                    // الفوتون مر عبر المجمع
                    if (photon.y >= camera.crystal.y && 
                        photon.y <= camera.crystal.y + camera.crystal.height) {
                        
                        // تم كشف الفوتون في البلورة
                        detectedEvents.push({
                            x: photon.x,
                            y: photon.y,
                            energy: photon.energy,
                            time: time
                        });
                        
                        photon.detected = true;
                        return true;
                    }
                }
            }
            
            return false;
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // خلفية
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            time++;
            rotationY += 0.01;

            // تحديث الفوتونات
            photons = photons.filter(photon => {
                if (!photon.detected) {
                    photon.x += photon.vx;
                    photon.y += photon.vy;
                    photon.life--;

                    // رسم الفوتون
                    ctx.beginPath();
                    ctx.fillStyle = '#ff6b6b';
                    ctx.arc(photon.x, photon.y, 3, 0, Math.PI * 2);
                    ctx.fill();

                    // فحص الكشف
                    checkPhotonDetection(photon);
                }

                return photon.life > 0 && !photon.detected;
            });

            // تنظيف الأحداث القديمة
            detectedEvents = detectedEvents.filter(event => time - event.time < 100);

            // رسم مكونات الكاميرا
            drawCollimator();
            drawCrystal();
            drawPMTs();

            // رسم المصدر
            ctx.fillStyle = '#feca57';
            ctx.beginPath();
            ctx.arc(100, 250, 15, 0, Math.PI * 2);
            ctx.fill();

            // تحديث الإحصائيات
            const totalPhotons = photons.length + detectedEvents.length;
            const efficiency = totalPhotons > 0 ? (detectedEvents.length / totalPhotons * 100).toFixed(1) : 0;
            
            document.getElementById('detected-photons').textContent = detectedEvents.length;
            document.getElementById('detection-efficiency').textContent = efficiency + '%';

            requestAnimationFrame(animate);
        }

        animate();

        return {
            canvas,
            emitPhotons: () => window.emitPhotons(),
            reset: () => {
                photons = [];
                detectedEvents = [];
                time = 0;
            }
        };
    }
}

// إنشاء مثيل عام للاستخدام
const threeDVisualizations = new ThreeDVisualizations();

// دوال مساعدة للاستخدام السهل
function create3DAtomModel(containerId, options = {}) {
    return threeDVisualizations.create3DAtomModel(containerId, options);
}

function create3DPETSimulator(containerId, options = {}) {
    return threeDVisualizations.create3DPETSimulator(containerId, options);
}

function create3DGammaCameraSimulator(containerId, options = {}) {
    return threeDVisualizations.create3DGammaCameraSimulator(containerId, options);
}