// Interactive Elements and Simulations

// Radioactive Decay Simulator
class DecaySimulator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.nuclei = [];
        this.isRunning = false;
        this.decayConstant = 0.1;
        this.timeStep = 100; // milliseconds
        this.init();
    }
    
    init() {
        this.createNuclei(100);
        this.render();
    }
    
    createNuclei(count) {
        this.nuclei = [];
        for (let i = 0; i < count; i++) {
            this.nuclei.push({
                id: i,
                active: true,
                x: Math.random() * 300,
                y: Math.random() * 200
            });
        }
    }
    
    start() {
        if (this.isRunning) return;
        this.isRunning = true;
        this.simulate();
    }
    
    stop() {
        this.isRunning = false;
    }
    
    reset() {
        this.stop();
        this.createNuclei(100);
        this.render();
    }
    
    simulate() {
        if (!this.isRunning) return;
        
        const activeNuclei = this.nuclei.filter(n => n.active);
        
        activeNuclei.forEach(nucleus => {
            if (Math.random() < this.decayConstant * this.timeStep / 1000) {
                nucleus.active = false;
            }
        });
        
        this.render();
        
        if (activeNuclei.length > 0) {
            setTimeout(() => this.simulate(), this.timeStep);
        } else {
            this.isRunning = false;
        }
    }
    
    render() {
        const activeCount = this.nuclei.filter(n => n.active).length;
        const decayedCount = this.nuclei.length - activeCount;
        
        // Update display
        if (this.container) {
            this.container.innerHTML = `
                <div class="decay-display">
                    <div class="decay-stats">
                        <div class="stat-item">
                            <span class="stat-label">النوى النشطة:</span>
                            <span class="stat-value active">${activeCount}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">النوى المضمحلة:</span>
                            <span class="stat-value decayed">${decayedCount}</span>
                        </div>
                    </div>
                    <div class="decay-chart">
                        <div class="chart-bar active" style="width: ${(activeCount / this.nuclei.length) * 100}%"></div>
                        <div class="chart-bar decayed" style="width: ${(decayedCount / this.nuclei.length) * 100}%"></div>
                    </div>
                    <div class="decay-controls">
                        <button onclick="decaySimulator.start()" class="btn-sim start">ابدأ</button>
                        <button onclick="decaySimulator.stop()" class="btn-sim stop">توقف</button>
                        <button onclick="decaySimulator.reset()" class="btn-sim reset">إعادة تعيين</button>
                    </div>
                </div>
            `;
        }
    }
}

// Atom Visualizer
class AtomVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.electrons = 3;
        this.protons = 3;
        this.neutrons = 3;
        this.init();
    }
    
    init() {
        this.render();
    }
    
    setElement(protons, neutrons, electrons) {
        this.protons = protons;
        this.neutrons = neutrons;
        this.electrons = electrons;
        this.render();
    }
    
    render() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="atom-display">
                    <div class="atom-controls">
                        <div class="control-group">
                            <label>البروتونات: ${this.protons}</label>
                            <input type="range" min="1" max="10" value="${this.protons}" 
                                   onchange="atomVisualizer.setElement(this.value, ${this.neutrons}, ${this.electrons})">
                        </div>
                        <div class="control-group">
                            <label>النيوترونات: ${this.neutrons}</label>
                            <input type="range" min="1" max="10" value="${this.neutrons}" 
                                   onchange="atomVisualizer.setElement(${this.protons}, this.value, ${this.electrons})">
                        </div>
                        <div class="control-group">
                            <label>الإلكترونات: ${this.electrons}</label>
                            <input type="range" min="1" max="10" value="${this.electrons}" 
                                   onchange="atomVisualizer.setElement(${this.protons}, ${this.neutrons}, this.value)">
                        </div>
                    </div>
                    <div class="atom-model">
                        <div class="nucleus">
                            <span class="particle-count">p: ${this.protons}, n: ${this.neutrons}</span>
                        </div>
                        ${this.renderElectronShells()}
                    </div>
                    <div class="atom-info">
                        <div class="info-item">
                            <span>العدد الذري (Z): ${this.protons}</span>
                        </div>
                        <div class="info-item">
                            <span>العدد الكتلي (A): ${this.protons + this.neutrons}</span>
                        </div>
                        <div class="info-item">
                            <span>الشحنة: ${this.protons - this.electrons > 0 ? '+' : ''}${this.protons - this.electrons}</span>
                        </div>
                    </div>
                </div>
            `;
        }
    }
    
    renderElectronShells() {
        let shells = '';
        let remainingElectrons = this.electrons;
        const shellCapacities = [2, 8, 18, 32]; // K, L, M, N shells
        
        for (let i = 0; i < Math.min(4, Math.ceil(this.electrons / 2)); i++) {
            const electronsInShell = Math.min(remainingElectrons, shellCapacities[i]);
            if (electronsInShell > 0) {
                shells += `
                    <div class="electron-shell shell-${i + 1}">
                        ${this.renderElectrons(electronsInShell, i + 1)}
                    </div>
                `;
                remainingElectrons -= electronsInShell;
            }
        }
        
        return shells;
    }
    
    renderElectrons(count, shellNumber) {
        let electrons = '';
        for (let i = 0; i < count; i++) {
            const angle = (360 / count) * i;
            electrons += `<div class="electron" style="transform: rotate(${angle}deg) translateX(${30 + shellNumber * 20}px) rotate(-${angle}deg);"></div>`;
        }
        return electrons;
    }
}

// Detector Response Simulator
class DetectorSimulator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.energy = 140; // keV
        this.detectorType = 'NaI';
        this.init();
    }
    
    init() {
        this.render();
    }
    
    setEnergy(energy) {
        this.energy = energy;
        this.updateResponse();
    }
    
    setDetectorType(type) {
        this.detectorType = type;
        this.updateResponse();
    }
    
    calculateResponse() {
        const detectorProperties = {
            'NaI': { efficiency: 0.9, resolution: 10 },
            'CsI': { efficiency: 0.7, resolution: 15 },
            'BGO': { efficiency: 0.8, resolution: 20 },
            'LSO': { efficiency: 0.85, resolution: 12 }
        };
        
        const props = detectorProperties[this.detectorType];
        const efficiency = props.efficiency * Math.exp(-this.energy / 1000); // Simplified
        const resolution = props.resolution;
        
        return { efficiency, resolution };
    }
    
    updateResponse() {
        const response = this.calculateResponse();
        this.render(response);
    }
    
    render(response = null) {
        if (!response) response = this.calculateResponse();
        
        if (this.container) {
            this.container.innerHTML = `
                <div class="detector-display">
                    <div class="detector-controls">
                        <div class="control-group">
                            <label>طاقة الفوتون: ${this.energy} keV</label>
                            <input type="range" min="50" max="500" value="${this.energy}" 
                                   onchange="detectorSimulator.setEnergy(this.value)">
                        </div>
                        <div class="control-group">
                            <label>نوع الكاشف:</label>
                            <select onchange="detectorSimulator.setDetectorType(this.value)">
                                <option value="NaI" ${this.detectorType === 'NaI' ? 'selected' : ''}>NaI:Tl</option>
                                <option value="CsI" ${this.detectorType === 'CsI' ? 'selected' : ''}>CsI:Tl</option>
                                <option value="BGO" ${this.detectorType === 'BGO' ? 'selected' : ''}>BGO</option>
                                <option value="LSO" ${this.detectorType === 'LSO' ? 'selected' : ''}>LSO</option>
                            </select>
                        </div>
                    </div>
                    <div class="detector-response">
                        <div class="response-chart">
                            <div class="chart-title">استجابة الكاشف</div>
                            <div class="efficiency-bar">
                                <div class="bar-fill" style="width: ${response.efficiency * 100}%"></div>
                                <span class="bar-label">الكفاءة: ${(response.efficiency * 100).toFixed(1)}%</span>
                            </div>
                            <div class="resolution-display">
                                <span>دقة الطاقة: ${response.resolution.toFixed(1)}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="detector-info">
                        <h4>خصائص ${this.detectorType}</h4>
                        <ul>
                            ${this.getDetectorInfo()}
                        </ul>
                    </div>
                </div>
            `;
        }
    }
    
    getDetectorInfo() {
        const info = {
            'NaI': [
                'إنتاج ضوء عالي (38 فوتون/keV)',
                'دقة طاقة ممتازة (~10%)',
                'استرطابي (يمتص الرطوبة)',
                'هش ويحتاج حماية'
            ],
            'CsI': [
                'بنية بلورية إبرية',
                'أقل استرطاباً من NaI',
                'إنتاج ضوء متوسط',
                'مناسب للتصوير الرقمي'
            ],
            'BGO': [
                'كثافة عالية (7.13 g/cm³)',
                'غير استرطابي',
                'إنتاج ضوء منخفض',
                'مناسب لـ PET و CT'
            ],
            'LSO': [
                'إنتاج ضوء عالي',
                'زمن اضمحلال سريع (40 ns)',
                'خصائص توقيت ممتازة',
                'مكلف نسبياً'
            ]
        };
        
        return info[this.detectorType].map(item => `<li>${item}</li>`).join('');
    }
}

// Initialize simulators when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize simulators if containers exist
    if (document.getElementById('decay-simulator')) {
        window.decaySimulator = new DecaySimulator('decay-simulator');
    }
    
    if (document.getElementById('atom-visualizer')) {
        window.atomVisualizer = new AtomVisualizer('atom-visualizer');
    }
    
    if (document.getElementById('detector-simulator')) {
        window.detectorSimulator = new DetectorSimulator('detector-simulator');
    }
});

// Add CSS for interactive elements
const interactiveStyles = `
<style>
.decay-display, .atom-display, .detector-display {
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.decay-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value.active {
    color: #10b981;
    font-weight: bold;
}

.stat-value.decayed {
    color: #6b7280;
    font-weight: bold;
}

.decay-chart {
    height: 20px;
    background: #f3f4f6;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
    display: flex;
}

.chart-bar.active {
    background: #10b981;
}

.chart-bar.decayed {
    background: #6b7280;
}

.decay-controls, .atom-controls, .detector-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.btn-sim {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.btn-sim.start {
    background: #10b981;
    color: white;
}

.btn-sim.stop {
    background: #ef4444;
    color: white;
}

.btn-sim.reset {
    background: #6b7280;
    color: white;
}

.atom-model {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 2rem auto;
}

.nucleus {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #f59e0b, #d97706);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: white;
    font-weight: bold;
}

.electron-shell {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

.shell-1 { width: 80px; height: 80px; }
.shell-2 { width: 120px; height: 120px; animation-duration: 15s; }
.shell-3 { width: 160px; height: 160px; animation-duration: 20s; }
.shell-4 { width: 200px; height: 200px; animation-duration: 25s; }

.electron {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    margin-top: -4px;
    margin-left: -4px;
}

.control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-size: 0.9rem;
    font-weight: 500;
}

.control-group input, .control-group select {
    padding: 0.25rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.atom-info, .detector-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 4px;
}

.info-item {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.response-chart {
    margin: 1rem 0;
}

.efficiency-bar {
    position: relative;
    height: 30px;
    background: #f3f4f6;
    border-radius: 15px;
    overflow: hidden;
    margin: 1rem 0;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    transition: width 0.3s ease;
}

.bar-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.9rem;
    font-weight: 500;
    color: #1f2937;
}

.resolution-display {
    text-align: center;
    font-weight: 500;
    color: #374151;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Additional Interactive Elements Styles */
.calculator-display, .dose-calculator, .collimator-sim {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 1rem 0;
}

.calculator-controls, .calculator-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.calculator-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.control-row, .input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-row label, .input-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.results-display {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.result-label {
    font-weight: 500;
    color: #4b5563;
}

.result-value {
    font-weight: 600;
    color: #1f2937;
    font-size: 1.1rem;
}

.dose-result {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
}

.safety-level {
    padding: 0.75rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.safety-level.safe {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.safety-level.caution {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.safety-level.danger {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.performance-item {
    margin-bottom: 1rem;
}

.perf-label {
    display: inline-block;
    width: 120px;
    font-weight: 500;
    color: #374151;
}

.perf-value {
    font-weight: 600;
    color: #1f2937;
    margin-left: 1rem;
}

.perf-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.bar-fill.resolution {
    background: linear-gradient(90deg, #10b981, #059669);
    height: 100%;
    transition: width 0.3s ease;
}

.bar-fill.sensitivity {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    height: 100%;
    transition: width 0.3s ease;
}

/* Generator Simulator Styles */
.generator-simulator {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.generator-info {
    margin-bottom: 1.5rem;
}

.isotope-info {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 1rem;
}

.parent-isotope, .daughter-isotope {
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 6px;
    min-width: 120px;
}

.arrow {
    font-size: 2rem;
    color: #3b82f6;
    font-weight: bold;
}

.activity-display {
    font-size: 1.5rem;
    font-weight: bold;
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
}

.activity-display.parent {
    background: #fee2e2;
    color: #991b1b;
}

.activity-display.daughter {
    background: #dbeafe;
    color: #1e40af;
}

.equilibrium-chart {
    margin: 1rem 0;
    text-align: center;
}

.equilibrium-info {
    background: #f0f9ff;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #3b82f6;
}

/* SPECT Reconstructor Styles */
.spect-reconstructor {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.reconstruction-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.reconstruction-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.projection-view, .sinogram-view, .reconstruction-view {
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 6px;
}

.projection-view h5, .sinogram-view h5, .reconstruction-view h5 {
    margin-bottom: 0.5rem;
    color: #374151;
}

.reconstruction-steps {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.step-number {
    background: #3b82f6;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.step-text {
    font-size: 0.9rem;
    color: #4b5563;
}

/* Spectrum Analyzer Styles */
.spectrum-analyzer {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.spectrum-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.spectrum-display {
    text-align: center;
    margin-bottom: 1rem;
}

.spectrum-info {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 6px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    font-weight: 500;
    color: #4b5563;
}

.info-value {
    font-weight: 600;
    color: #1f2937;
}

/* Quality Analyzer Styles */
.quality-analyzer {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quality-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.quality-results {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: center;
}

.quality-scores {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.score-item {
    display: grid;
    grid-template-columns: 100px 1fr 50px;
    gap: 1rem;
    align-items: center;
}

.score-bar {
    height: 20px;
    background: #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    transition: width 0.3s ease;
}

.overall-quality {
    text-align: center;
}

.quality-score {
    padding: 2rem;
    border-radius: 50%;
    display: inline-block;
    min-width: 120px;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.quality-score.excellent {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.quality-score.very-good {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.quality-score.good {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.quality-score.acceptable {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.quality-score.poor {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.score-number {
    font-size: 2rem;
    font-weight: bold;
}

.score-grade {
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

@media (max-width: 768px) {
    .isotope-info {
        flex-direction: column;
        gap: 1rem;
    }

    .arrow {
        transform: rotate(90deg);
    }

    .reconstruction-display {
        grid-template-columns: 1fr;
    }

    .quality-results {
        grid-template-columns: 1fr;
    }

    .reconstruction-steps {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', interactiveStyles);

// Additional Interactive Elements and Simulators

// Half-Life Calculator
class HalfLifeCalculator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.initialActivity = 1000;
        this.halfLife = 6;
        this.currentTime = 0;
        this.init();
    }

    init() {
        this.render();
    }

    calculateActivity(time) {
        const decayConstant = 0.693 / this.halfLife;
        return this.initialActivity * Math.exp(-decayConstant * time);
    }

    setParameters(activity, halfLife) {
        this.initialActivity = activity;
        this.halfLife = halfLife;
        this.render();
    }

    setTime(time) {
        this.currentTime = time;
        this.render();
    }

    render() {
        if (this.container) {
            const currentActivity = this.calculateActivity(this.currentTime);
            const percentRemaining = (currentActivity / this.initialActivity) * 100;

            this.container.innerHTML = `
                <div class="calculator-display">
                    <h4>حاسبة عمر النصف</h4>
                    <div class="calculator-controls">
                        <div class="control-row">
                            <label>النشاط الأولي (Bq):</label>
                            <input type="number" value="${this.initialActivity}"
                                   onchange="halfLifeCalc.setParameters(this.value, ${this.halfLife})">
                        </div>
                        <div class="control-row">
                            <label>عمر النصف (ساعة):</label>
                            <input type="number" value="${this.halfLife}"
                                   onchange="halfLifeCalc.setParameters(${this.initialActivity}, this.value)">
                        </div>
                        <div class="control-row">
                            <label>الزمن (ساعة): ${this.currentTime}</label>
                            <input type="range" min="0" max="${this.halfLife * 5}" value="${this.currentTime}"
                                   onchange="halfLifeCalc.setTime(this.value)">
                        </div>
                    </div>
                    <div class="results-display">
                        <div class="result-item">
                            <span class="result-label">النشاط الحالي:</span>
                            <span class="result-value">${currentActivity.toFixed(2)} Bq</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">النسبة المتبقية:</span>
                            <span class="result-value">${percentRemaining.toFixed(1)}%</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">عدد أعمار النصف:</span>
                            <span class="result-value">${(this.currentTime / this.halfLife).toFixed(2)}</span>
                        </div>
                    </div>
                    <div class="decay-curve">
                        <canvas id="decayCurve" width="300" height="150"></canvas>
                    </div>
                </div>
            `;

            this.drawDecayCurve();
        }
    }

    drawDecayCurve() {
        const canvas = document.getElementById('decayCurve');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw axes
            ctx.strokeStyle = '#333';
            ctx.beginPath();
            ctx.moveTo(40, 130);
            ctx.lineTo(280, 130);
            ctx.moveTo(40, 130);
            ctx.lineTo(40, 20);
            ctx.stroke();

            // Draw decay curve
            ctx.strokeStyle = '#2563eb';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x <= 240; x++) {
                const time = (x / 240) * this.halfLife * 5;
                const activity = this.calculateActivity(time);
                const y = 130 - (activity / this.initialActivity) * 110;

                if (x === 0) {
                    ctx.moveTo(40 + x, y);
                } else {
                    ctx.lineTo(40 + x, y);
                }
            }
            ctx.stroke();

            // Mark current point
            const currentX = 40 + (this.currentTime / (this.halfLife * 5)) * 240;
            const currentY = 130 - (this.calculateActivity(this.currentTime) / this.initialActivity) * 110;

            ctx.fillStyle = '#ef4444';
            ctx.beginPath();
            ctx.arc(currentX, currentY, 4, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
}

// Dose Rate Calculator
class DoseRateCalculator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.activity = 1000; // MBq
        this.distance = 1; // meters
        this.shieldingThickness = 0; // mm lead
        this.init();
    }

    init() {
        this.render();
    }

    calculateDoseRate() {
        // Simplified dose rate calculation for Tc-99m
        const gamma = 0.0187; // μSv/h per MBq at 1m for Tc-99m
        let doseRate = (gamma * this.activity) / (this.distance * this.distance);

        // Apply shielding attenuation (lead for 140 keV)
        if (this.shieldingThickness > 0) {
            const mu = 0.23; // cm⁻¹ for lead at 140 keV
            const attenuationFactor = Math.exp(-mu * this.shieldingThickness / 10);
            doseRate *= attenuationFactor;
        }

        return doseRate;
    }

    render() {
        if (this.container) {
            const doseRate = this.calculateDoseRate();

            this.container.innerHTML = `
                <div class="dose-calculator">
                    <h4>حاسبة معدل الجرعة</h4>
                    <div class="calculator-grid">
                        <div class="input-group">
                            <label>النشاط (MBq):</label>
                            <input type="number" value="${this.activity}"
                                   onchange="doseCalc.activity = this.value; doseCalc.render()">
                        </div>
                        <div class="input-group">
                            <label>المسافة (متر):</label>
                            <input type="number" step="0.1" value="${this.distance}"
                                   onchange="doseCalc.distance = this.value; doseCalc.render()">
                        </div>
                        <div class="input-group">
                            <label>سمك الحماية الرصاصية (mm):</label>
                            <input type="number" value="${this.shieldingThickness}"
                                   onchange="doseCalc.shieldingThickness = this.value; doseCalc.render()">
                        </div>
                    </div>
                    <div class="dose-result">
                        <div class="result-main">
                            <span class="result-label">معدل الجرعة:</span>
                            <span class="result-value">${doseRate.toFixed(2)} μSv/h</span>
                        </div>
                        <div class="result-secondary">
                            <span>الجرعة اليومية (8 ساعات): ${(doseRate * 8).toFixed(2)} μSv</span>
                        </div>
                    </div>
                    <div class="safety-info">
                        <div class="safety-level ${this.getSafetyLevel(doseRate)}">
                            <i class="fas fa-shield-alt"></i>
                            <span>${this.getSafetyMessage(doseRate)}</span>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    getSafetyLevel(doseRate) {
        if (doseRate < 2.5) return 'safe';
        if (doseRate < 25) return 'caution';
        return 'danger';
    }

    getSafetyMessage(doseRate) {
        if (doseRate < 2.5) return 'مستوى آمن - لا حاجة لاحتياطات خاصة';
        if (doseRate < 25) return 'مستوى حذر - احتياطات أساسية مطلوبة';
        return 'مستوى خطر - احتياطات صارمة مطلوبة';
    }
}

// Collimator Performance Simulator
class CollimatorSimulator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.collimatorType = 'parallel';
        this.holeSize = 2; // mm
        this.septalThickness = 0.2; // mm
        this.length = 25; // mm
        this.distance = 10; // cm
        this.init();
    }

    init() {
        this.render();
    }

    calculateResolution() {
        let resolution;
        switch (this.collimatorType) {
            case 'parallel':
                resolution = this.holeSize * (this.length + this.distance * 10) / this.length;
                break;
            case 'converging':
                resolution = this.holeSize * (1 + this.distance * 10 / this.length);
                break;
            case 'diverging':
                resolution = this.holeSize * (1 - this.distance * 10 / this.length);
                break;
            case 'pinhole':
                resolution = this.holeSize * this.distance * 10 / 100;
                break;
            default:
                resolution = this.holeSize;
        }
        return Math.max(resolution, 0.1);
    }

    calculateSensitivity() {
        const geometricEfficiency = Math.pow(this.holeSize, 4) / Math.pow(this.length, 2);
        return geometricEfficiency * 1000; // Normalized
    }

    render() {
        if (this.container) {
            const resolution = this.calculateResolution();
            const sensitivity = this.calculateSensitivity();

            this.container.innerHTML = `
                <div class="collimator-sim">
                    <h4>محاكي أداء المجمع</h4>
                    <div class="collimator-controls">
                        <div class="control-group">
                            <label>نوع المجمع:</label>
                            <select onchange="collimatorSim.collimatorType = this.value; collimatorSim.render()">
                                <option value="parallel" ${this.collimatorType === 'parallel' ? 'selected' : ''}>متوازي</option>
                                <option value="converging" ${this.collimatorType === 'converging' ? 'selected' : ''}>متقارب</option>
                                <option value="diverging" ${this.collimatorType === 'diverging' ? 'selected' : ''}>متباعد</option>
                                <option value="pinhole" ${this.collimatorType === 'pinhole' ? 'selected' : ''}>ثقب واحد</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>قطر الثقب (mm): ${this.holeSize}</label>
                            <input type="range" min="1" max="5" step="0.1" value="${this.holeSize}"
                                   onchange="collimatorSim.holeSize = this.value; collimatorSim.render()">
                        </div>
                        <div class="control-group">
                            <label>طول المجمع (mm): ${this.length}</label>
                            <input type="range" min="10" max="50" value="${this.length}"
                                   onchange="collimatorSim.length = this.value; collimatorSim.render()">
                        </div>
                        <div class="control-group">
                            <label>المسافة (cm): ${this.distance}</label>
                            <input type="range" min="5" max="30" value="${this.distance}"
                                   onchange="collimatorSim.distance = this.value; collimatorSim.render()">
                        </div>
                    </div>
                    <div class="performance-display">
                        <div class="performance-item">
                            <span class="perf-label">الدقة المكانية:</span>
                            <span class="perf-value">${resolution.toFixed(2)} mm</span>
                            <div class="perf-bar">
                                <div class="bar-fill resolution" style="width: ${Math.min(resolution * 10, 100)}%"></div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <span class="perf-label">الحساسية:</span>
                            <span class="perf-value">${sensitivity.toFixed(0)} (نسبي)</span>
                            <div class="perf-bar">
                                <div class="bar-fill sensitivity" style="width: ${Math.min(sensitivity / 10, 100)}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="collimator-diagram">
                        <canvas id="collimatorCanvas" width="300" height="200"></canvas>
                    </div>
                </div>
            `;

            this.drawCollimatorDiagram();
        }
    }

    drawCollimatorDiagram() {
        const canvas = document.getElementById('collimatorCanvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw collimator based on type
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;

            switch (this.collimatorType) {
                case 'parallel':
                    this.drawParallelCollimator(ctx);
                    break;
                case 'converging':
                    this.drawConvergingCollimator(ctx);
                    break;
                case 'diverging':
                    this.drawDivergingCollimator(ctx);
                    break;
                case 'pinhole':
                    this.drawPinholeCollimator(ctx);
                    break;
            }
        }
    }

    drawParallelCollimator(ctx) {
        // Draw parallel holes
        for (let i = 0; i < 5; i++) {
            const x = 50 + i * 40;
            ctx.beginPath();
            ctx.moveTo(x, 50);
            ctx.lineTo(x, 150);
            ctx.moveTo(x + 10, 50);
            ctx.lineTo(x + 10, 150);
            ctx.stroke();
        }

        // Draw septa
        ctx.fillStyle = '#666';
        for (let i = 0; i < 6; i++) {
            const x = 45 + i * 40;
            ctx.fillRect(x, 50, 5, 100);
        }
    }

    drawConvergingCollimator(ctx) {
        // Draw converging holes
        ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const x1 = 50 + i * 40;
            const x2 = 120 + i * 20;
            ctx.moveTo(x1, 50);
            ctx.lineTo(x2, 150);
            ctx.moveTo(x1 + 10, 50);
            ctx.lineTo(x2 + 5, 150);
        }
        ctx.stroke();
    }

    drawDivergingCollimator(ctx) {
        // Draw diverging holes
        ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const x1 = 80 + i * 20;
            const x2 = 50 + i * 40;
            ctx.moveTo(x1, 50);
            ctx.lineTo(x2, 150);
            ctx.moveTo(x1 + 5, 50);
            ctx.lineTo(x2 + 10, 150);
        }
        ctx.stroke();
    }

    drawPinholeCollimator(ctx) {
        // Draw pinhole
        ctx.fillStyle = '#666';
        ctx.fillRect(50, 50, 200, 20);
        ctx.fillRect(50, 130, 200, 20);

        // Draw aperture
        ctx.clearRect(145, 50, 10, 100);

        // Draw radiation paths
        ctx.strokeStyle = '#f59e0b';
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(30, 30);
        ctx.lineTo(150, 100);
        ctx.lineTo(270, 170);
        ctx.moveTo(30, 170);
        ctx.lineTo(150, 100);
        ctx.lineTo(270, 30);
        ctx.stroke();
        ctx.setLineDash([]);
    }
}

// Initialize additional simulators
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('halflife-calculator')) {
        window.halfLifeCalc = new HalfLifeCalculator('halflife-calculator');
    }

    if (document.getElementById('dose-calculator')) {
        window.doseCalc = new DoseRateCalculator('dose-calculator');
    }

    if (document.getElementById('collimator-simulator')) {
        window.collimatorSim = new CollimatorSimulator('collimator-simulator');
    }
});

// Additional Interactive Elements

// Energy Spectrum Analyzer
class SpectrumAnalyzer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.isotope = 'Tc99m';
        this.resolution = 10; // %
        this.init();
    }

    init() {
        this.render();
    }

    getIsotopeData(isotope) {
        const data = {
            'Tc99m': { energy: 140, abundance: 89, name: 'تكنيشيوم-99m' },
            'I123': { energy: 159, abundance: 83, name: 'يود-123' },
            'Tl201': { energy: 167, abundance: 10, name: 'ثاليوم-201' },
            'Ga67': { energy: 93, abundance: 40, name: 'جاليوم-67' }
        };
        return data[isotope] || data['Tc99m'];
    }

    render() {
        if (this.container) {
            const isotopeData = this.getIsotopeData(this.isotope);

            this.container.innerHTML = `
                <div class="spectrum-analyzer">
                    <h4>محلل طيف الطاقة</h4>
                    <div class="spectrum-controls">
                        <div class="control-group">
                            <label>النظير المشع:</label>
                            <select onchange="spectrumAnalyzer.isotope = this.value; spectrumAnalyzer.render()">
                                <option value="Tc99m" ${this.isotope === 'Tc99m' ? 'selected' : ''}>Tc-99m</option>
                                <option value="I123" ${this.isotope === 'I123' ? 'selected' : ''}>I-123</option>
                                <option value="Tl201" ${this.isotope === 'Tl201' ? 'selected' : ''}>Tl-201</option>
                                <option value="Ga67" ${this.isotope === 'Ga67' ? 'selected' : ''}>Ga-67</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>دقة الطاقة (%): ${this.resolution}</label>
                            <input type="range" min="5" max="20" value="${this.resolution}"
                                   onchange="spectrumAnalyzer.resolution = this.value; spectrumAnalyzer.render()">
                        </div>
                    </div>
                    <div class="spectrum-display">
                        <canvas id="spectrumCanvas" width="400" height="250"></canvas>
                    </div>
                    <div class="spectrum-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">النظير:</span>
                                <span class="info-value">${isotopeData.name}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">طاقة الذروة:</span>
                                <span class="info-value">${isotopeData.energy} keV</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الوفرة:</span>
                                <span class="info-value">${isotopeData.abundance}%</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">FWHM:</span>
                                <span class="info-value">${(isotopeData.energy * this.resolution / 100).toFixed(1)} keV</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            this.drawSpectrum();
        }
    }

    drawSpectrum() {
        const canvas = document.getElementById('spectrumCanvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            const isotopeData = this.getIsotopeData(this.isotope);

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw axes
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(50, 200);
            ctx.lineTo(350, 200);
            ctx.moveTo(50, 200);
            ctx.lineTo(50, 50);
            ctx.stroke();

            // Draw spectrum
            const peakX = 50 + (isotopeData.energy / 200) * 300;
            const sigma = (isotopeData.energy * this.resolution / 100) / 2.35;

            ctx.strokeStyle = '#2563eb';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 50; x <= 350; x++) {
                const energy = ((x - 50) / 300) * 200;
                const gaussian = Math.exp(-0.5 * Math.pow((energy - isotopeData.energy) / sigma, 2));
                const y = 200 - gaussian * 140;

                if (x === 50) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // Add labels
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('الطاقة (keV)', 180, 230);
            ctx.save();
            ctx.translate(20, 125);
            ctx.rotate(-Math.PI / 2);
            ctx.fillText('العدد', 0, 0);
            ctx.restore();
        }
    }
}

// Radiation Safety Calculator
class SafetyCalculator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.activity = 1000; // MBq
        this.time = 8; // hours
        this.distance = 1; // meters
        this.shielding = 0; // mm lead
        this.init();
    }

    init() {
        this.render();
    }

    calculateExposure() {
        // Simplified calculation for Tc-99m
        const gamma = 0.0187; // μSv/h per MBq at 1m
        let doseRate = (gamma * this.activity) / (this.distance * this.distance);

        // Apply shielding
        if (this.shielding > 0) {
            const mu = 0.23; // cm⁻¹ for lead at 140 keV
            doseRate *= Math.exp(-mu * this.shielding / 10);
        }

        const totalDose = doseRate * this.time;
        return { doseRate, totalDose };
    }

    getSafetyRecommendations(doseRate) {
        if (doseRate < 2.5) {
            return {
                level: 'safe',
                message: 'مستوى آمن - لا حاجة لاحتياطات خاصة',
                recommendations: ['مراقبة دورية', 'تسجيل الجرعات']
            };
        } else if (doseRate < 25) {
            return {
                level: 'caution',
                message: 'مستوى حذر - احتياطات أساسية مطلوبة',
                recommendations: ['تقليل وقت التعرض', 'زيادة المسافة', 'مراقبة شخصية']
            };
        } else {
            return {
                level: 'danger',
                message: 'مستوى خطر - احتياطات صارمة مطلوبة',
                recommendations: ['حماية إشعاعية', 'تقليل الوقت للحد الأدنى', 'مراقبة مستمرة', 'تدريب متخصص']
            };
        }
    }

    render() {
        if (this.container) {
            const { doseRate, totalDose } = this.calculateExposure();
            const safety = this.getSafetyRecommendations(doseRate);

            this.container.innerHTML = `
                <div class="safety-calculator">
                    <h4>حاسبة السلامة الإشعاعية</h4>
                    <div class="safety-inputs">
                        <div class="input-row">
                            <label>النشاط (MBq):</label>
                            <input type="number" value="${this.activity}"
                                   onchange="safetyCalc.activity = this.value; safetyCalc.render()">
                        </div>
                        <div class="input-row">
                            <label>وقت التعرض (ساعة):</label>
                            <input type="number" value="${this.time}"
                                   onchange="safetyCalc.time = this.value; safetyCalc.render()">
                        </div>
                        <div class="input-row">
                            <label>المسافة (متر):</label>
                            <input type="number" step="0.1" value="${this.distance}"
                                   onchange="safetyCalc.distance = this.value; safetyCalc.render()">
                        </div>
                        <div class="input-row">
                            <label>سمك الحماية الرصاصية (mm):</label>
                            <input type="number" value="${this.shielding}"
                                   onchange="safetyCalc.shielding = this.value; safetyCalc.render()">
                        </div>
                    </div>
                    <div class="safety-results">
                        <div class="result-main">
                            <div class="result-item">
                                <span class="result-label">معدل الجرعة:</span>
                                <span class="result-value">${doseRate.toFixed(2)} μSv/h</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">الجرعة الكلية:</span>
                                <span class="result-value">${totalDose.toFixed(2)} μSv</span>
                            </div>
                        </div>
                        <div class="safety-status ${safety.level}">
                            <i class="fas fa-shield-alt"></i>
                            <span>${safety.message}</span>
                        </div>
                        <div class="safety-recommendations">
                            <h5>التوصيات:</h5>
                            <ul>
                                ${safety.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }
    }
}

// Image Quality Analyzer
class ImageQualityAnalyzer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.resolution = 10; // mm
        this.contrast = 15; // %
        this.noise = 5; // %
        this.uniformity = 3; // %
        this.init();
    }

    init() {
        this.render();
    }

    calculateQualityScore() {
        // Simplified quality scoring
        const resolutionScore = Math.max(0, 100 - this.resolution * 5);
        const contrastScore = Math.min(100, this.contrast * 3);
        const noiseScore = Math.max(0, 100 - this.noise * 10);
        const uniformityScore = Math.max(0, 100 - this.uniformity * 15);

        const overallScore = (resolutionScore + contrastScore + noiseScore + uniformityScore) / 4;

        return {
            resolution: resolutionScore,
            contrast: contrastScore,
            noise: noiseScore,
            uniformity: uniformityScore,
            overall: overallScore
        };
    }

    getQualityGrade(score) {
        if (score >= 90) return { grade: 'ممتاز', class: 'excellent' };
        if (score >= 80) return { grade: 'جيد جداً', class: 'very-good' };
        if (score >= 70) return { grade: 'جيد', class: 'good' };
        if (score >= 60) return { grade: 'مقبول', class: 'acceptable' };
        return { grade: 'ضعيف', class: 'poor' };
    }

    render() {
        if (this.container) {
            const scores = this.calculateQualityScore();
            const grade = this.getQualityGrade(scores.overall);

            this.container.innerHTML = `
                <div class="quality-analyzer">
                    <h4>محلل جودة الصورة</h4>
                    <div class="quality-controls">
                        <div class="control-group">
                            <label>الدقة المكانية (mm): ${this.resolution}</label>
                            <input type="range" min="5" max="20" value="${this.resolution}"
                                   onchange="qualityAnalyzer.resolution = this.value; qualityAnalyzer.render()">
                        </div>
                        <div class="control-group">
                            <label>التباين (%): ${this.contrast}</label>
                            <input type="range" min="5" max="30" value="${this.contrast}"
                                   onchange="qualityAnalyzer.contrast = this.value; qualityAnalyzer.render()">
                        </div>
                        <div class="control-group">
                            <label>الضوضاء (%): ${this.noise}</label>
                            <input type="range" min="1" max="15" value="${this.noise}"
                                   onchange="qualityAnalyzer.noise = this.value; qualityAnalyzer.render()">
                        </div>
                        <div class="control-group">
                            <label>التجانس (%): ${this.uniformity}</label>
                            <input type="range" min="1" max="10" value="${this.uniformity}"
                                   onchange="qualityAnalyzer.uniformity = this.value; qualityAnalyzer.render()">
                        </div>
                    </div>
                    <div class="quality-results">
                        <div class="quality-scores">
                            <div class="score-item">
                                <span class="score-label">الدقة:</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: ${scores.resolution}%"></div>
                                </div>
                                <span class="score-value">${scores.resolution.toFixed(0)}</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">التباين:</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: ${scores.contrast}%"></div>
                                </div>
                                <span class="score-value">${scores.contrast.toFixed(0)}</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">الضوضاء:</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: ${scores.noise}%"></div>
                                </div>
                                <span class="score-value">${scores.noise.toFixed(0)}</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">التجانس:</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: ${scores.uniformity}%"></div>
                                </div>
                                <span class="score-value">${scores.uniformity.toFixed(0)}</span>
                            </div>
                        </div>
                        <div class="overall-quality">
                            <div class="quality-score ${grade.class}">
                                <span class="score-number">${scores.overall.toFixed(0)}</span>
                                <span class="score-grade">${grade.grade}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }
}

// Initialize additional simulators
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('spectrum-analyzer')) {
        window.spectrumAnalyzer = new SpectrumAnalyzer('spectrum-analyzer');
    }

    if (document.getElementById('safety-calculator')) {
        window.safetyCalc = new SafetyCalculator('safety-calculator');
    }

    if (document.getElementById('quality-analyzer')) {
        window.qualityAnalyzer = new ImageQualityAnalyzer('quality-analyzer');
    }
});

// Additional Specialized Interactive Elements

// Generator Equilibrium Simulator
class GeneratorSimulator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.parentHalfLife = 66; // Mo-99 hours
        this.daughterHalfLife = 6; // Tc-99m hours
        this.time = 0;
        this.maxTime = 200;
        this.init();
    }

    init() {
        this.render();
    }

    calculateActivities(time) {
        const lambdaP = 0.693 / this.parentHalfLife;
        const lambdaD = 0.693 / this.daughterHalfLife;

        const parentActivity = Math.exp(-lambdaP * time);
        let daughterActivity;

        if (Math.abs(lambdaP - lambdaD) < 0.001) {
            // Special case for equal decay constants
            daughterActivity = lambdaP * time * Math.exp(-lambdaP * time);
        } else {
            daughterActivity = (lambdaP / (lambdaD - lambdaP)) *
                              (Math.exp(-lambdaP * time) - Math.exp(-lambdaD * time));
        }

        return { parent: parentActivity, daughter: daughterActivity };
    }

    render() {
        if (this.container) {
            const activities = this.calculateActivities(this.time);

            this.container.innerHTML = `
                <div class="generator-simulator">
                    <h4>محاكي توازن المولد</h4>
                    <div class="generator-info">
                        <div class="isotope-info">
                            <div class="parent-isotope">
                                <h5>الأصل: Mo-99</h5>
                                <p>عمر النصف: ${this.parentHalfLife} ساعة</p>
                                <div class="activity-display parent">
                                    ${(activities.parent * 100).toFixed(1)}%
                                </div>
                            </div>
                            <div class="arrow">→</div>
                            <div class="daughter-isotope">
                                <h5>الابن: Tc-99m</h5>
                                <p>عمر النصف: ${this.daughterHalfLife} ساعة</p>
                                <div class="activity-display daughter">
                                    ${(activities.daughter * 100).toFixed(1)}%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="time-control">
                        <label>الزمن (ساعة): ${this.time}</label>
                        <input type="range" min="0" max="${this.maxTime}" value="${this.time}"
                               onchange="generatorSim.time = this.value; generatorSim.render()">
                    </div>
                    <div class="equilibrium-chart">
                        <canvas id="equilibriumCanvas" width="400" height="200"></canvas>
                    </div>
                    <div class="equilibrium-info">
                        <p><strong>التوازن العلماني:</strong> يحدث عندما يكون عمر نصف الأصل >> عمر نصف الابن</p>
                        <p><strong>وقت التوازن:</strong> حوالي ${(4 * this.daughterHalfLife).toFixed(0)} ساعة</p>
                    </div>
                </div>
            `;

            this.drawEquilibriumChart();
        }
    }

    drawEquilibriumChart() {
        const canvas = document.getElementById('equilibriumCanvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw axes
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(50, 170);
            ctx.lineTo(350, 170);
            ctx.moveTo(50, 170);
            ctx.lineTo(50, 30);
            ctx.stroke();

            // Draw parent decay curve
            ctx.strokeStyle = '#ef4444';
            ctx.lineWidth = 2;
            ctx.beginPath();
            for (let x = 0; x <= 300; x++) {
                const time = (x / 300) * this.maxTime;
                const activities = this.calculateActivities(time);
                const y = 170 - activities.parent * 140;

                if (x === 0) {
                    ctx.moveTo(50 + x, y);
                } else {
                    ctx.lineTo(50 + x, y);
                }
            }
            ctx.stroke();

            // Draw daughter growth curve
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            for (let x = 0; x <= 300; x++) {
                const time = (x / 300) * this.maxTime;
                const activities = this.calculateActivities(time);
                const y = 170 - activities.daughter * 140;

                if (x === 0) {
                    ctx.moveTo(50 + x, y);
                } else {
                    ctx.lineTo(50 + x, y);
                }
            }
            ctx.stroke();

            // Mark current time
            const currentX = 50 + (this.time / this.maxTime) * 300;
            ctx.strokeStyle = '#10b981';
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(currentX, 30);
            ctx.lineTo(currentX, 170);
            ctx.stroke();
            ctx.setLineDash([]);

            // Add legend
            ctx.fillStyle = '#ef4444';
            ctx.fillRect(60, 40, 15, 3);
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('Mo-99', 80, 45);

            ctx.fillStyle = '#3b82f6';
            ctx.fillRect(60, 55, 15, 3);
            ctx.fillText('Tc-99m', 80, 60);
        }
    }
}

// SPECT Reconstruction Visualizer
class SPECTReconstructor {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.projectionAngle = 0;
        this.filterType = 'ramp';
        this.projections = [];
        this.init();
    }

    init() {
        this.generateProjections();
        this.render();
    }

    generateProjections() {
        // Simplified phantom (circle with hot spot)
        this.projections = [];
        for (let angle = 0; angle < 180; angle += 10) {
            const projection = this.calculateProjection(angle);
            this.projections.push(projection);
        }
    }

    calculateProjection(angle) {
        // Simplified projection calculation
        const projection = [];
        for (let i = 0; i < 64; i++) {
            const value = Math.exp(-Math.pow((i - 32) / 10, 2)) +
                         0.3 * Math.exp(-Math.pow((i - 20) / 5, 2));
            projection.push(value);
        }
        return projection;
    }

    applyFilter(projection) {
        // Simplified filtering
        const filtered = [...projection];
        if (this.filterType === 'ramp') {
            for (let i = 1; i < filtered.length - 1; i++) {
                filtered[i] = projection[i] - 0.25 * (projection[i-1] + projection[i+1]);
            }
        }
        return filtered;
    }

    render() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="spect-reconstructor">
                    <h4>محاكي إعادة بناء SPECT</h4>
                    <div class="reconstruction-controls">
                        <div class="control-group">
                            <label>زاوية الإسقاط: ${this.projectionAngle}°</label>
                            <input type="range" min="0" max="170" step="10" value="${this.projectionAngle}"
                                   onchange="spectRecon.projectionAngle = this.value; spectRecon.render()">
                        </div>
                        <div class="control-group">
                            <label>نوع المرشح:</label>
                            <select onchange="spectRecon.filterType = this.value; spectRecon.render()">
                                <option value="none" ${this.filterType === 'none' ? 'selected' : ''}>بدون مرشح</option>
                                <option value="ramp" ${this.filterType === 'ramp' ? 'selected' : ''}>مرشح المنحدر</option>
                                <option value="hamming" ${this.filterType === 'hamming' ? 'selected' : ''}>مرشح هامينغ</option>
                            </select>
                        </div>
                    </div>
                    <div class="reconstruction-display">
                        <div class="projection-view">
                            <h5>الإسقاط الحالي</h5>
                            <canvas id="projectionCanvas" width="200" height="150"></canvas>
                        </div>
                        <div class="sinogram-view">
                            <h5>السينوجرام</h5>
                            <canvas id="sinogramCanvas" width="200" height="150"></canvas>
                        </div>
                        <div class="reconstruction-view">
                            <h5>الصورة المعاد بناؤها</h5>
                            <canvas id="reconstructionCanvas" width="150" height="150"></canvas>
                        </div>
                    </div>
                    <div class="reconstruction-steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-text">اكتساب الإسقاطات</div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-text">تطبيق المرشح</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-text">الإسقاط الخلفي</div>
                        </div>
                    </div>
                </div>
            `;

            this.drawProjection();
            this.drawSinogram();
            this.drawReconstruction();
        }
    }

    drawProjection() {
        const canvas = document.getElementById('projectionCanvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const projectionIndex = Math.floor(this.projectionAngle / 10);
            const projection = this.projections[projectionIndex] || this.projections[0];
            const filtered = this.applyFilter(projection);

            // Draw original projection
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            for (let i = 0; i < projection.length; i++) {
                const x = (i / projection.length) * 180 + 10;
                const y = 130 - projection[i] * 80;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // Draw filtered projection if filter is applied
            if (this.filterType !== 'none') {
                ctx.strokeStyle = '#ef4444';
                ctx.lineWidth = 1;
                ctx.beginPath();
                for (let i = 0; i < filtered.length; i++) {
                    const x = (i / filtered.length) * 180 + 10;
                    const y = 130 - (filtered[i] + 1) * 40;

                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
            }
        }
    }

    drawSinogram() {
        const canvas = document.getElementById('sinogramCanvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw simplified sinogram
            for (let angle = 0; angle < this.projections.length; angle++) {
                const projection = this.projections[angle];
                for (let i = 0; i < projection.length; i++) {
                    const intensity = Math.floor(projection[i] * 255);
                    ctx.fillStyle = `rgb(${intensity}, ${intensity}, ${intensity})`;
                    ctx.fillRect(
                        (i / projection.length) * 180 + 10,
                        (angle / this.projections.length) * 130 + 10,
                        3, 7
                    );
                }
            }

            // Highlight current angle
            const currentY = (this.projectionAngle / 170) * 130 + 10;
            ctx.strokeStyle = '#ef4444';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(10, currentY);
            ctx.lineTo(190, currentY);
            ctx.stroke();
        }
    }

    drawReconstruction() {
        const canvas = document.getElementById('reconstructionCanvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw simplified reconstructed image (phantom)
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // Main circle
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 40);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            gradient.addColorStop(1, 'rgba(100, 100, 100, 0.3)');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 40, 0, 2 * Math.PI);
            ctx.fill();

            // Hot spot
            const hotSpot = ctx.createRadialGradient(centerX - 15, centerY - 10, 0, centerX - 15, centerY - 10, 10);
            hotSpot.addColorStop(0, 'rgba(255, 255, 255, 1)');
            hotSpot.addColorStop(1, 'rgba(200, 200, 200, 0.5)');

            ctx.fillStyle = hotSpot;
            ctx.beginPath();
            ctx.arc(centerX - 15, centerY - 10, 10, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
}

// Initialize additional simulators
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('generator-simulator')) {
        window.generatorSim = new GeneratorSimulator('generator-simulator');
    }

    if (document.getElementById('spect-reconstructor')) {
        window.spectRecon = new SPECTReconstructor('spect-reconstructor');
    }
});
