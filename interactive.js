// Interactive Elements and Simulations

// Radioactive Decay Simulator
class DecaySimulator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.nuclei = [];
        this.isRunning = false;
        this.decayConstant = 0.1;
        this.timeStep = 100; // milliseconds
        this.init();
    }
    
    init() {
        this.createNuclei(100);
        this.render();
    }
    
    createNuclei(count) {
        this.nuclei = [];
        for (let i = 0; i < count; i++) {
            this.nuclei.push({
                id: i,
                active: true,
                x: Math.random() * 300,
                y: Math.random() * 200
            });
        }
    }
    
    start() {
        if (this.isRunning) return;
        this.isRunning = true;
        this.simulate();
    }
    
    stop() {
        this.isRunning = false;
    }
    
    reset() {
        this.stop();
        this.createNuclei(100);
        this.render();
    }
    
    simulate() {
        if (!this.isRunning) return;
        
        const activeNuclei = this.nuclei.filter(n => n.active);
        
        activeNuclei.forEach(nucleus => {
            if (Math.random() < this.decayConstant * this.timeStep / 1000) {
                nucleus.active = false;
            }
        });
        
        this.render();
        
        if (activeNuclei.length > 0) {
            setTimeout(() => this.simulate(), this.timeStep);
        } else {
            this.isRunning = false;
        }
    }
    
    render() {
        const activeCount = this.nuclei.filter(n => n.active).length;
        const decayedCount = this.nuclei.length - activeCount;
        
        // Update display
        if (this.container) {
            this.container.innerHTML = `
                <div class="decay-display">
                    <div class="decay-stats">
                        <div class="stat-item">
                            <span class="stat-label">النوى النشطة:</span>
                            <span class="stat-value active">${activeCount}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">النوى المضمحلة:</span>
                            <span class="stat-value decayed">${decayedCount}</span>
                        </div>
                    </div>
                    <div class="decay-chart">
                        <div class="chart-bar active" style="width: ${(activeCount / this.nuclei.length) * 100}%"></div>
                        <div class="chart-bar decayed" style="width: ${(decayedCount / this.nuclei.length) * 100}%"></div>
                    </div>
                    <div class="decay-controls">
                        <button onclick="decaySimulator.start()" class="btn-sim start">ابدأ</button>
                        <button onclick="decaySimulator.stop()" class="btn-sim stop">توقف</button>
                        <button onclick="decaySimulator.reset()" class="btn-sim reset">إعادة تعيين</button>
                    </div>
                </div>
            `;
        }
    }
}

// Atom Visualizer
class AtomVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.electrons = 3;
        this.protons = 3;
        this.neutrons = 3;
        this.init();
    }
    
    init() {
        this.render();
    }
    
    setElement(protons, neutrons, electrons) {
        this.protons = protons;
        this.neutrons = neutrons;
        this.electrons = electrons;
        this.render();
    }
    
    render() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="atom-display">
                    <div class="atom-controls">
                        <div class="control-group">
                            <label>البروتونات: ${this.protons}</label>
                            <input type="range" min="1" max="10" value="${this.protons}" 
                                   onchange="atomVisualizer.setElement(this.value, ${this.neutrons}, ${this.electrons})">
                        </div>
                        <div class="control-group">
                            <label>النيوترونات: ${this.neutrons}</label>
                            <input type="range" min="1" max="10" value="${this.neutrons}" 
                                   onchange="atomVisualizer.setElement(${this.protons}, this.value, ${this.electrons})">
                        </div>
                        <div class="control-group">
                            <label>الإلكترونات: ${this.electrons}</label>
                            <input type="range" min="1" max="10" value="${this.electrons}" 
                                   onchange="atomVisualizer.setElement(${this.protons}, ${this.neutrons}, this.value)">
                        </div>
                    </div>
                    <div class="atom-model">
                        <div class="nucleus">
                            <span class="particle-count">p: ${this.protons}, n: ${this.neutrons}</span>
                        </div>
                        ${this.renderElectronShells()}
                    </div>
                    <div class="atom-info">
                        <div class="info-item">
                            <span>العدد الذري (Z): ${this.protons}</span>
                        </div>
                        <div class="info-item">
                            <span>العدد الكتلي (A): ${this.protons + this.neutrons}</span>
                        </div>
                        <div class="info-item">
                            <span>الشحنة: ${this.protons - this.electrons > 0 ? '+' : ''}${this.protons - this.electrons}</span>
                        </div>
                    </div>
                </div>
            `;
        }
    }
    
    renderElectronShells() {
        let shells = '';
        let remainingElectrons = this.electrons;
        const shellCapacities = [2, 8, 18, 32]; // K, L, M, N shells
        
        for (let i = 0; i < Math.min(4, Math.ceil(this.electrons / 2)); i++) {
            const electronsInShell = Math.min(remainingElectrons, shellCapacities[i]);
            if (electronsInShell > 0) {
                shells += `
                    <div class="electron-shell shell-${i + 1}">
                        ${this.renderElectrons(electronsInShell, i + 1)}
                    </div>
                `;
                remainingElectrons -= electronsInShell;
            }
        }
        
        return shells;
    }
    
    renderElectrons(count, shellNumber) {
        let electrons = '';
        for (let i = 0; i < count; i++) {
            const angle = (360 / count) * i;
            electrons += `<div class="electron" style="transform: rotate(${angle}deg) translateX(${30 + shellNumber * 20}px) rotate(-${angle}deg);"></div>`;
        }
        return electrons;
    }
}

// Detector Response Simulator
class DetectorSimulator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.energy = 140; // keV
        this.detectorType = 'NaI';
        this.init();
    }
    
    init() {
        this.render();
    }
    
    setEnergy(energy) {
        this.energy = energy;
        this.updateResponse();
    }
    
    setDetectorType(type) {
        this.detectorType = type;
        this.updateResponse();
    }
    
    calculateResponse() {
        const detectorProperties = {
            'NaI': { efficiency: 0.9, resolution: 10 },
            'CsI': { efficiency: 0.7, resolution: 15 },
            'BGO': { efficiency: 0.8, resolution: 20 },
            'LSO': { efficiency: 0.85, resolution: 12 }
        };
        
        const props = detectorProperties[this.detectorType];
        const efficiency = props.efficiency * Math.exp(-this.energy / 1000); // Simplified
        const resolution = props.resolution;
        
        return { efficiency, resolution };
    }
    
    updateResponse() {
        const response = this.calculateResponse();
        this.render(response);
    }
    
    render(response = null) {
        if (!response) response = this.calculateResponse();
        
        if (this.container) {
            this.container.innerHTML = `
                <div class="detector-display">
                    <div class="detector-controls">
                        <div class="control-group">
                            <label>طاقة الفوتون: ${this.energy} keV</label>
                            <input type="range" min="50" max="500" value="${this.energy}" 
                                   onchange="detectorSimulator.setEnergy(this.value)">
                        </div>
                        <div class="control-group">
                            <label>نوع الكاشف:</label>
                            <select onchange="detectorSimulator.setDetectorType(this.value)">
                                <option value="NaI" ${this.detectorType === 'NaI' ? 'selected' : ''}>NaI:Tl</option>
                                <option value="CsI" ${this.detectorType === 'CsI' ? 'selected' : ''}>CsI:Tl</option>
                                <option value="BGO" ${this.detectorType === 'BGO' ? 'selected' : ''}>BGO</option>
                                <option value="LSO" ${this.detectorType === 'LSO' ? 'selected' : ''}>LSO</option>
                            </select>
                        </div>
                    </div>
                    <div class="detector-response">
                        <div class="response-chart">
                            <div class="chart-title">استجابة الكاشف</div>
                            <div class="efficiency-bar">
                                <div class="bar-fill" style="width: ${response.efficiency * 100}%"></div>
                                <span class="bar-label">الكفاءة: ${(response.efficiency * 100).toFixed(1)}%</span>
                            </div>
                            <div class="resolution-display">
                                <span>دقة الطاقة: ${response.resolution.toFixed(1)}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="detector-info">
                        <h4>خصائص ${this.detectorType}</h4>
                        <ul>
                            ${this.getDetectorInfo()}
                        </ul>
                    </div>
                </div>
            `;
        }
    }
    
    getDetectorInfo() {
        const info = {
            'NaI': [
                'إنتاج ضوء عالي (38 فوتون/keV)',
                'دقة طاقة ممتازة (~10%)',
                'استرطابي (يمتص الرطوبة)',
                'هش ويحتاج حماية'
            ],
            'CsI': [
                'بنية بلورية إبرية',
                'أقل استرطاباً من NaI',
                'إنتاج ضوء متوسط',
                'مناسب للتصوير الرقمي'
            ],
            'BGO': [
                'كثافة عالية (7.13 g/cm³)',
                'غير استرطابي',
                'إنتاج ضوء منخفض',
                'مناسب لـ PET و CT'
            ],
            'LSO': [
                'إنتاج ضوء عالي',
                'زمن اضمحلال سريع (40 ns)',
                'خصائص توقيت ممتازة',
                'مكلف نسبياً'
            ]
        };
        
        return info[this.detectorType].map(item => `<li>${item}</li>`).join('');
    }
}

// Initialize simulators when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize simulators if containers exist
    if (document.getElementById('decay-simulator')) {
        window.decaySimulator = new DecaySimulator('decay-simulator');
    }
    
    if (document.getElementById('atom-visualizer')) {
        window.atomVisualizer = new AtomVisualizer('atom-visualizer');
    }
    
    if (document.getElementById('detector-simulator')) {
        window.detectorSimulator = new DetectorSimulator('detector-simulator');
    }
});

// Add CSS for interactive elements
const interactiveStyles = `
<style>
.decay-display, .atom-display, .detector-display {
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.decay-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value.active {
    color: #10b981;
    font-weight: bold;
}

.stat-value.decayed {
    color: #6b7280;
    font-weight: bold;
}

.decay-chart {
    height: 20px;
    background: #f3f4f6;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
    display: flex;
}

.chart-bar.active {
    background: #10b981;
}

.chart-bar.decayed {
    background: #6b7280;
}

.decay-controls, .atom-controls, .detector-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.btn-sim {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.btn-sim.start {
    background: #10b981;
    color: white;
}

.btn-sim.stop {
    background: #ef4444;
    color: white;
}

.btn-sim.reset {
    background: #6b7280;
    color: white;
}

.atom-model {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 2rem auto;
}

.nucleus {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #f59e0b, #d97706);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: white;
    font-weight: bold;
}

.electron-shell {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

.shell-1 { width: 80px; height: 80px; }
.shell-2 { width: 120px; height: 120px; animation-duration: 15s; }
.shell-3 { width: 160px; height: 160px; animation-duration: 20s; }
.shell-4 { width: 200px; height: 200px; animation-duration: 25s; }

.electron {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    margin-top: -4px;
    margin-left: -4px;
}

.control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-size: 0.9rem;
    font-weight: 500;
}

.control-group input, .control-group select {
    padding: 0.25rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.atom-info, .detector-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 4px;
}

.info-item {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.response-chart {
    margin: 1rem 0;
}

.efficiency-bar {
    position: relative;
    height: 30px;
    background: #f3f4f6;
    border-radius: 15px;
    overflow: hidden;
    margin: 1rem 0;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    transition: width 0.3s ease;
}

.bar-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.9rem;
    font-weight: 500;
    color: #1f2937;
}

.resolution-display {
    text-align: center;
    font-weight: 500;
    color: #374151;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', interactiveStyles);
