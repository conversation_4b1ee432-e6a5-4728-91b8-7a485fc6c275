<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العناصر التفاعلية - منصة التصوير الطبي النووي</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .interactive-gallery {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .gallery-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .gallery-title {
            font-size: 2.5rem;
            color: #1e40af;
            margin-bottom: 1rem;
        }
        
        .gallery-subtitle {
            font-size: 1.2rem;
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .category-section {
            margin-bottom: 3rem;
        }
        
        .category-title {
            font-size: 1.8rem;
            color: #1f2937;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .elements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .element-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .element-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .element-header {
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            padding: 1rem;
            border-bottom: 1px solid #d1d5db;
        }
        
        .element-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .element-type {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .element-content {
            padding: 1.5rem;
        }
        
        .element-description {
            color: #4b5563;
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .element-placeholder {
            background: #f9fafb;
            border: 2px dashed #d1d5db;
            border-radius: 6px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .element-icon {
            font-size: 2rem;
            color: #6b7280;
            margin-bottom: 1rem;
        }
        
        .launch-button {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .launch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .element-features {
            margin-top: 1rem;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 0.25rem 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .features-list li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .back-button {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .interactive-gallery {
                padding: 1rem;
            }
            
            .elements-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </button>
    
    <div class="interactive-gallery">
        <div class="gallery-header">
            <h1 class="gallery-title">العناصر التفاعلية والمحاكيات العلمية</h1>
            <p class="gallery-subtitle">مجموعة شاملة من الأدوات التفاعلية لتعلم فيزياء وتكنولوجيا التصوير الطبي النووي</p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">75+</span>
                    <span class="stat-label">عنصر تفاعلي</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">25+</span>
                    <span class="stat-label">محاكي علمي</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">40+</span>
                    <span class="stat-label">معادلة رياضية</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">150+</span>
                    <span class="stat-label">رسم ومخطط</span>
                </div>
            </div>
        </div>
        
        <!-- Physics Simulators -->
        <div class="category-section">
            <h2 class="category-title">
                <i class="fas fa-atom"></i>
                المحاكيات الفيزيائية
            </h2>
            <div class="elements-grid">
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">محاكي الاضمحلال الإشعاعي</h3>
                        <span class="element-type">محاكي فيزيائي</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">محاكاة تفاعلية لعملية الاضمحلال الإشعاعي مع رسوم بيانية حية وحسابات دقيقة</p>
                        <div class="element-placeholder">
                            <i class="fas fa-radiation element-icon"></i>
                            <div id="decay-simulator"></div>
                        </div>
                        <div class="element-features">
                            <ul class="features-list">
                                <li>رسم بياني للاضمحلال الأسي</li>
                                <li>حساب عمر النصف</li>
                                <li>مقارنة النظائر المختلفة</li>
                                <li>عرض المعادلات الرياضية</li>
                            </ul>
                        </div>
                        <button class="launch-button" onclick="initDecaySimulator()">
                            <i class="fas fa-play"></i> تشغيل المحاكي
                        </button>
                    </div>
                </div>
                
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">نموذج الذرة ثلاثي الأبعاد</h3>
                        <span class="element-type">نموذج تفاعلي</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">نموذج ثلاثي الأبعاد قابل للدوران يوضح بنية الذرة والنواة</p>
                        <div class="element-placeholder">
                            <i class="fas fa-atom element-icon"></i>
                            <div id="atom-model-3d"></div>
                        </div>
                        <div class="element-features">
                            <ul class="features-list">
                                <li>دوران حر في جميع الاتجاهات</li>
                                <li>عرض المكونات بالتفصيل</li>
                                <li>تكبير وتصغير تفاعلي</li>
                                <li>معلومات عن كل مكون</li>
                            </ul>
                        </div>
                        <button class="launch-button" onclick="initAtomModel()">
                            <i class="fas fa-cube"></i> عرض النموذج
                        </button>
                    </div>
                </div>
                
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">محاكي عملية الفناء</h3>
                        <span class="element-type">محاكي PET</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">محاكاة عملية فناء البوزيترون مع الإلكترون وإنتاج فوتونات 511 keV</p>
                        <div class="element-placeholder">
                            <i class="fas fa-bolt element-icon"></i>
                            <div id="annihilation-simulator"></div>
                        </div>
                        <div class="element-features">
                            <ul class="features-list">
                                <li>رسم متحرك لعملية الفناء</li>
                                <li>حساب طاقة الفوتونات</li>
                                <li>زوايا الانبعاث</li>
                                <li>إحصائيات التفاعل</li>
                            </ul>
                        </div>
                        <button class="launch-button" onclick="initAnnihilationSim()">
                            <i class="fas fa-play"></i> بدء المحاكاة
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Calculation Tools -->
        <div class="category-section">
            <h2 class="category-title">
                <i class="fas fa-calculator"></i>
                أدوات الحساب التفاعلية
            </h2>
            <div class="elements-grid">
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">حاسبة عمر النصف</h3>
                        <span class="element-type">أداة حساب</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">حساب عمر النصف والنشاط الإشعاعي مع رسوم بيانية تفاعلية</p>
                        <div class="element-placeholder">
                            <i class="fas fa-clock element-icon"></i>
                            <div id="halflife-calculator"></div>
                        </div>
                        <button class="launch-button" onclick="initHalfLifeCalc()">
                            <i class="fas fa-calculator"></i> فتح الحاسبة
                        </button>
                    </div>
                </div>
                
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">حاسبة معدل الجرعة</h3>
                        <span class="element-type">أداة سلامة</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">حساب معدل الجرعة الإشعاعية مع تأثير المسافة والحماية</p>
                        <div class="element-placeholder">
                            <i class="fas fa-shield-alt element-icon"></i>
                            <div id="dose-calculator"></div>
                        </div>
                        <button class="launch-button" onclick="initDoseCalc()">
                            <i class="fas fa-calculator"></i> حساب الجرعة
                        </button>
                    </div>
                </div>
                
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">حاسبة السلامة الإشعاعية</h3>
                        <span class="element-type">أداة سلامة</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">تقييم شامل للسلامة الإشعاعية مع التوصيات</p>
                        <div class="element-placeholder">
                            <i class="fas fa-user-shield element-icon"></i>
                            <div id="safety-calculator"></div>
                        </div>
                        <button class="launch-button" onclick="initSafetyCalc()">
                            <i class="fas fa-shield-alt"></i> تقييم السلامة
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Analysis Tools -->
        <div class="category-section">
            <h2 class="category-title">
                <i class="fas fa-chart-line"></i>
                أدوات التحليل والتقييم
            </h2>
            <div class="elements-grid">
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">محلل طيف الطاقة</h3>
                        <span class="element-type">أداة تحليل</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">تحليل طيف الطاقة للنظائر المختلفة مع حساب دقة الطاقة</p>
                        <div class="element-placeholder">
                            <i class="fas fa-wave-square element-icon"></i>
                            <div id="spectrum-analyzer"></div>
                        </div>
                        <button class="launch-button" onclick="initSpectrumAnalyzer()">
                            <i class="fas fa-chart-line"></i> تحليل الطيف
                        </button>
                    </div>
                </div>
                
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">محلل جودة الصورة</h3>
                        <span class="element-type">أداة تقييم</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">تقييم شامل لجودة الصورة مع معايير الأداء</p>
                        <div class="element-placeholder">
                            <i class="fas fa-image element-icon"></i>
                            <div id="quality-analyzer"></div>
                        </div>
                        <button class="launch-button" onclick="initQualityAnalyzer()">
                            <i class="fas fa-microscope"></i> تحليل الجودة
                        </button>
                    </div>
                </div>
                
                <div class="element-card">
                    <div class="element-header">
                        <h3 class="element-title">محاكي أداء المجمع</h3>
                        <span class="element-type">محاكي تقني</span>
                    </div>
                    <div class="element-content">
                        <p class="element-description">محاكاة أداء المجمعات المختلفة مع حساب الدقة والحساسية</p>
                        <div class="element-placeholder">
                            <i class="fas fa-th element-icon"></i>
                            <div id="collimator-simulator"></div>
                        </div>
                        <button class="launch-button" onclick="initCollimatorSim()">
                            <i class="fas fa-cogs"></i> محاكاة المجمع
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
    <script src="interactive.js"></script>
    <script src="equations-concepts.js"></script>
    <script src="figures-diagrams.js"></script>
    
    <script>
        // Initialize interactive elements
        function initDecaySimulator() {
            if (!window.decaySimulator) {
                window.decaySimulator = new DecaySimulator('decay-simulator');
            }
        }
        
        function initAtomModel() {
            if (!window.atomModel) {
                window.atomModel = new AtomModel('atom-model-3d');
            }
        }
        
        function initAnnihilationSim() {
            if (!window.annihilationSim) {
                window.annihilationSim = new AnnihilationSimulator('annihilation-simulator');
            }
        }
        
        function initHalfLifeCalc() {
            if (!window.halfLifeCalc) {
                window.halfLifeCalc = new HalfLifeCalculator('halflife-calculator');
            }
        }
        
        function initDoseCalc() {
            if (!window.doseCalc) {
                window.doseCalc = new DoseRateCalculator('dose-calculator');
            }
        }
        
        function initSafetyCalc() {
            if (!window.safetyCalc) {
                window.safetyCalc = new SafetyCalculator('safety-calculator');
            }
        }
        
        function initSpectrumAnalyzer() {
            if (!window.spectrumAnalyzer) {
                window.spectrumAnalyzer = new SpectrumAnalyzer('spectrum-analyzer');
            }
        }
        
        function initQualityAnalyzer() {
            if (!window.qualityAnalyzer) {
                window.qualityAnalyzer = new ImageQualityAnalyzer('quality-analyzer');
            }
        }
        
        function initCollimatorSim() {
            if (!window.collimatorSim) {
                window.collimatorSim = new CollimatorSimulator('collimator-simulator');
            }
        }
    </script>
</body>
</html>
